# WebRTC 基础框架

一个基于 NestJS 的 WebRTC 框架，专注于**流媒体传输**和**数据通道通信**，支持浏览器和移动端。

## 🚀 快速开始

### 1. 启动服务器

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm run start:dev
```

### 2. 访问测试页面

打开浏览器访问：`http://localhost:3000/test.html`

## 📋 核心功能

### ✅ 已实现功能

- **信令服务器**: WebSocket 信令处理 SDP 交换和 ICE 候选
- **流媒体传输**: 支持音视频流、屏幕共享
- **数据通道**: 实时数据传输和文件传输
- **STUN/TURN 配置**: 支持 NAT 穿透
- **多质量级别**: low/medium/high 三个质量档位
- **客户端 SDK**: 浏览器和移动端 JavaScript SDK

### 🏗️ 架构组件

```
src/
├── signaling/          # 信令服务器
│   └── signaling.gateway.ts
├── webrtc/            # WebRTC 配置和控制器
│   ├── webrtc-config.service.ts
│   ├── webrtc.controller.ts
│   └── webrtc.module.ts
├── media/             # 流媒体处理
│   └── media.service.ts
└── datachannel/       # 数据通道
    └── datachannel.service.ts
```

## 🔌 API 端点

### 配置相关
- `GET /api/webrtc/config?quality=medium` - 获取 WebRTC 配置
- `GET /api/webrtc/config/audio-only` - 音频专用配置
- `GET /api/webrtc/config/screen-share` - 屏幕共享配置

### 流媒体
- `POST /api/webrtc/streams/register` - 注册媒体流
- `GET /api/webrtc/streams/:streamId` - 获取流信息
- `POST /api/webrtc/streams/:streamId/stats` - 更新流统计

### 数据通道
- `POST /api/webrtc/data-channels/register` - 注册数据通道
- `GET /api/webrtc/data-channels/:connectionId/messages` - 获取消息历史

### 监控
- `GET /api/webrtc/health` - 健康检查
- `GET /api/webrtc/stats/*` - 各种统计信息

## 📡 WebSocket 事件

### 信令事件

```javascript
// 客户端发送
socket.emit('ready');                    // 标记准备就绪
socket.emit('offer', { data: offer });   // 发送 Offer
socket.emit('answer', { data: answer }); // 发送 Answer
socket.emit('ice-candidate', { data: candidate }); // 发送 ICE 候选

// 服务器发送
socket.on('user-connected', callback);   // 用户连接
socket.on('user-ready', callback);       // 用户准备就绪
socket.on('offer', callback);            // 收到 Offer
socket.on('answer', callback);           // 收到 Answer
socket.on('ice-candidate', callback);    // 收到 ICE 候选
```

## 🛠️ 客户端 SDK 使用

### 基本使用

```javascript
// 初始化 SDK
const sdk = new WebRTCSDK('http://localhost:3000');
await sdk.init('medium'); // 质量级别: low/medium/high

// 事件监听
sdk.on('connected', (data) => {
    console.log('Connected:', data.clientId);
});

sdk.on('remote-stream', (data) => {
    videoElement.srcObject = data.stream;
});

// 获取本地媒体流
const stream = await sdk.getLocalStream();
localVideo.srcObject = stream;

// 创建连接
await sdk.createOffer();
```

### 数据通道

```javascript
// 发送文本消息
sdk.sendDataChannelMessage('Hello World', 'text');

// 发送文件
await sdk.sendFile(file);

// 监听消息
sdk.on('data-channel-message', (message) => {
    console.log('Received:', message.data);
});
```

## ⚙️ 配置选项

### 环境变量

```bash
PORT=3000                    # 服务器端口
TURN_URL=turn:your-server    # TURN 服务器地址
TURN_USERNAME=username       # TURN 用户名
TURN_CREDENTIAL=password     # TURN 密码
```

### 质量级别

| 级别 | 分辨率 | 帧率 | 视频码率 | 音频码率 |
|------|--------|------|----------|----------|
| low | 640x480 | 15fps | 500kbps | 32kbps |
| medium | 1280x720 | 24fps | 1.5Mbps | 64kbps |
| high | 1920x1080 | 30fps | 3Mbps | 128kbps |

## 🧪 测试

### 本地测试

1. 启动服务器：`pnpm run start:dev`
2. 打开两个浏览器标签页访问：`http://localhost:3000/test.html`
3. 在两个页面中点击 "Initialize" 和 "Ready"
4. 在一个页面中点击 "Start Video" 和 "Create Offer"
5. 测试视频通话和数据传输

### 功能测试

- ✅ 视频通话
- ✅ 音频通话  
- ✅ 屏幕共享
- ✅ 数据通道消息
- ✅ 文件传输
- ✅ 连接统计

## 📈 监控和调试

### 健康检查

```bash
curl http://localhost:3000/api/webrtc/health
```

### 获取统计信息

```javascript
// 客户端获取连接统计
const stats = await sdk.getStats();
console.log('WebRTC Stats:', stats);
```

### 服务器统计

- 信令统计：`GET /api/webrtc/stats/signaling`
- 媒体统计：`GET /api/webrtc/stats/media`  
- 数据通道统计：`GET /api/webrtc/stats/data-channels`

## 🔒 生产环境配置

### TURN 服务器

```bash
# 设置 TURN 服务器环境变量
export TURN_URL="turn:your-turn-server.com:3478"
export TURN_USERNAME="your-username"
export TURN_CREDENTIAL="your-password"
```

### HTTPS 配置

WebRTC 在生产环境需要 HTTPS，建议使用 Nginx 反向代理：

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🤝 扩展开发

### 添加新功能

1. **自定义信令**: 在 `SignalingGateway` 中添加新的事件处理
2. **媒体处理**: 在 `MediaService` 中扩展流处理逻辑
3. **数据通道**: 在 `DataChannelService` 中添加新的消息类型

### 集成示例

```javascript
// 集成到现有应用
import { WebRTCModule } from './webrtc/webrtc.module';

@Module({
  imports: [WebRTCModule],
  // ...
})
export class YourAppModule {}
```

## 📚 相关资源

- [WebRTC API 文档](https://developer.mozilla.org/en-US/docs/Web/API/WebRTC_API)
- [Socket.IO 文档](https://socket.io/docs/)
- [NestJS WebSocket](https://docs.nestjs.com/websockets/gateways)

---

这个框架提供了 WebRTC 的核心功能，可以根据具体需求进行扩展和定制。
