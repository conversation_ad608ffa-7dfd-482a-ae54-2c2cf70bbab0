# 🐕 Robot Dog WebRTC 服务框架

这是一个为机器狗项目构建的完整 WebRTC 服务框架，基于 NestJS 开发，提供实时视频流传输、远程控制和双向通信功能。

## 🏗️ 架构概览

### 核心模块

1. **WebSocket 网关** (`src/websocket/websocket.gateway.ts`)
   - 实时双向通信
   - 房间管理
   - 信令传输
   - 客户端连接管理

2. **信令服务器** (`src/webrtc/signaling.service.ts`)
   - WebRTC 连接建立
   - SDP 交换处理
   - ICE 候选管理
   - 对等连接状态跟踪

3. **媒体流处理** (`src/media/media-stream.service.ts`)
   - 多质量级别支持
   - 自适应码率调整
   - 流统计监控
   - 编解码器优化

4. **机器狗控制** (`src/robot/robot-control.service.ts`)
   - 运动控制命令
   - 摄像头控制
   - 状态监控
   - 命令队列管理

## 🚀 快速开始

### 1. 安装依赖

```bash
pnpm install
```

### 2. 启动服务器

```bash
# 开发模式
pnpm run start:dev

# 生产模式
pnpm run start:prod
```

### 3. 访问测试客户端

打开浏览器访问：`http://localhost:3000/test-client.html`

## 📡 API 端点

### WebRTC 配置
- `GET /api/webrtc/config?quality=medium` - 获取 WebRTC 配置
- `GET /api/webrtc/health` - 健康检查

### 房间管理
- `GET /api/webrtc/rooms` - 获取活跃房间列表
- `GET /api/webrtc/rooms/:roomId/peers` - 获取房间内的对等端

### 机器狗控制
- `GET /api/webrtc/robots` - 获取所有机器狗
- `GET /api/webrtc/robots/:robotId` - 获取特定机器狗状态
- `POST /api/webrtc/robots/:robotId/move` - 发送运动控制命令
- `POST /api/webrtc/robots/:robotId/camera` - 发送摄像头控制命令
- `POST /api/webrtc/robots/:robotId/emergency-stop` - 紧急停止

### 统计信息
- `GET /api/webrtc/stats/signaling` - 信令服务统计
- `GET /api/webrtc/stats/media` - 媒体流统计
- `GET /api/webrtc/stats/robots` - 机器狗控制统计

## 🔌 WebSocket 事件

### 客户端发送事件

```javascript
// 加入房间
socket.emit('join-room', { 
  roomId: 'room-123', 
  clientType: 'controller' // 或 'robot'
});

// WebRTC 信令
socket.emit('webrtc-signal', {
  type: 'offer', // 或 'answer', 'ice-candidate'
  data: sdpOffer,
  targetId: 'peer-id' // 可选，指定目标
});

// 机器狗控制
socket.emit('robot-control', {
  type: 'move',
  data: {
    direction: 'forward',
    speed: 50
  }
});
```

### 服务器发送事件

```javascript
// 用户加入/离开
socket.on('user-joined', (data) => {
  console.log(`${data.clientId} joined as ${data.clientType}`);
});

// WebRTC 信令
socket.on('webrtc-signal', (message) => {
  // 处理 offer/answer/ice-candidate
});

// 机器狗状态
socket.on('robot-status', (status) => {
  console.log('Robot status:', status);
});
```

## 🎮 控制命令格式

### 运动控制

```javascript
const movementCommand = {
  type: 'move',
  direction: 'forward', // forward, backward, left, right, up, down
  speed: 50, // 0-100
  duration: 1000, // 毫秒，可选
  angle: 90 // 旋转角度，可选
};
```

### 摄像头控制

```javascript
const cameraCommand = {
  type: 'pan', // pan, tilt, zoom, focus, preset
  angle: { x: 45, y: -30 }, // 水平和垂直角度
  zoom: 75, // 0-100
  preset: 'home' // 预设位置名称
};
```

## 📊 质量级别配置

框架支持 4 个预定义的质量级别：

| 级别 | 分辨率 | 帧率 | 视频码率 | 音频码率 |
|------|--------|------|----------|----------|
| low | 640x480 | 15fps | 500kbps | 32kbps |
| medium | 1280x720 | 24fps | 1.5Mbps | 64kbps |
| high | 1920x1080 | 30fps | 3Mbps | 128kbps |
| ultra | 3840x2160 | 60fps | 8Mbps | 256kbps |

## 🔧 配置选项

### 环境变量

```bash
PORT=3000                    # 服务器端口
NODE_ENV=development         # 环境模式
```

### CORS 配置

在 `src/main.ts` 中配置允许的域名：

```typescript
app.enableCors({
  origin: [
    'http://localhost:3000',
    'https://your-domain.com',
  ],
  credentials: true,
});
```

## 🛠️ 开发指南

### 添加新的控制命令

1. 在 `RobotControlService` 中定义新的命令类型
2. 在 `WebSocketGateway` 中添加事件处理
3. 在 `WebRTCController` 中添加 REST API 端点

### 自定义媒体配置

在 `MediaStreamService` 中修改 `qualityLevels` 对象来调整质量设置。

### 扩展信令功能

在 `SignalingService` 中添加新的信令消息类型和处理逻辑。

## 🧪 测试

### 单元测试

```bash
pnpm run test
```

### E2E 测试

```bash
pnpm run test:e2e
```

### 手动测试

1. 启动服务器
2. 打开多个浏览器标签页访问测试客户端
3. 使用不同的客户端类型（controller/robot）加入同一房间
4. 测试视频流和控制命令

## 📈 监控和调试

### 日志级别

框架使用 NestJS 内置的 Logger，支持以下级别：
- `error` - 错误信息
- `warn` - 警告信息
- `log` - 一般信息
- `debug` - 调试信息

### 性能监控

通过 `/api/webrtc/health` 端点获取实时统计信息：

```json
{
  "status": "healthy",
  "services": {
    "signaling": {
      "totalPeers": 4,
      "totalRooms": 2,
      "connectedPeers": 3
    },
    "media": {
      "totalStreams": 2,
      "avgBitrate": 1500000
    },
    "robots": {
      "totalRobots": 1,
      "connectedRobots": 1,
      "totalCommands": 0
    }
  }
}
```

## 🔒 安全考虑

1. **HTTPS**: 生产环境必须使用 HTTPS
2. **认证**: 建议添加 JWT 或其他认证机制
3. **TURN 服务器**: 配置 TURN 服务器以支持 NAT 穿透
4. **速率限制**: 对 API 端点添加速率限制
5. **输入验证**: 验证所有用户输入

## 🚀 部署

### Docker 部署

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "run", "start:prod"]
```

### 生产环境配置

1. 配置反向代理（Nginx）
2. 设置 SSL 证书
3. 配置 TURN/STUN 服务器
4. 设置监控和日志收集

## 📚 相关资源

- [WebRTC API 文档](https://developer.mozilla.org/en-US/docs/Web/API/WebRTC_API)
- [Socket.IO 文档](https://socket.io/docs/)
- [NestJS 文档](https://docs.nestjs.com/)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个框架！
