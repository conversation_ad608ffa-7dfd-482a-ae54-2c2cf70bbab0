import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';

interface WebRTCMessage {
  type: 'offer' | 'answer' | 'ice-candidate' | 'join-room' | 'leave-room';
  data: any;
  roomId?: string;
  targetId?: string;
}

interface RobotControlMessage {
  type: 'move' | 'rotate' | 'stop' | 'camera-control';
  data: {
    direction?: string;
    speed?: number;
    angle?: number;
    cameraAngle?: { x: number; y: number };
  };
}

@WebSocketGateway({
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
  },
  namespace: '/webrtc',
})
export class WebSocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(WebSocketGateway.name);
  private rooms = new Map<string, Set<string>>(); // roomId -> Set of socketIds
  private clients = new Map<string, { socketId: string; roomId?: string }>(); // clientId -> client info

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
    this.clients.set(client.id, { socketId: client.id });
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
    
    // Remove client from room if they were in one
    const clientInfo = this.clients.get(client.id);
    if (clientInfo?.roomId) {
      this.leaveRoom(client, clientInfo.roomId);
    }
    
    this.clients.delete(client.id);
  }

  @SubscribeMessage('join-room')
  handleJoinRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { roomId: string; clientType: 'robot' | 'controller' }
  ) {
    const { roomId, clientType } = data;
    
    this.logger.log(`Client ${client.id} joining room ${roomId} as ${clientType}`);
    
    // Leave current room if in one
    const currentClientInfo = this.clients.get(client.id);
    if (currentClientInfo?.roomId) {
      this.leaveRoom(client, currentClientInfo.roomId);
    }
    
    // Join new room
    if (!this.rooms.has(roomId)) {
      this.rooms.set(roomId, new Set());
    }
    
    this.rooms.get(roomId)!.add(client.id);
    this.clients.set(client.id, { socketId: client.id, roomId });
    
    client.join(roomId);
    
    // Notify others in the room
    client.to(roomId).emit('user-joined', {
      clientId: client.id,
      clientType,
    });
    
    // Send current room members to the new client
    const roomMembers = Array.from(this.rooms.get(roomId)!);
    client.emit('room-members', { members: roomMembers });
  }

  @SubscribeMessage('leave-room')
  handleLeaveRoom(@ConnectedSocket() client: Socket) {
    const clientInfo = this.clients.get(client.id);
    if (clientInfo?.roomId) {
      this.leaveRoom(client, clientInfo.roomId);
    }
  }

  @SubscribeMessage('webrtc-signal')
  handleWebRTCSignal(
    @ConnectedSocket() client: Socket,
    @MessageBody() message: WebRTCMessage
  ) {
    this.logger.log(`WebRTC signal from ${client.id}: ${message.type}`);
    
    const clientInfo = this.clients.get(client.id);
    if (!clientInfo?.roomId) {
      client.emit('error', { message: 'Not in a room' });
      return;
    }
    
    if (message.targetId) {
      // Send to specific client
      client.to(message.targetId).emit('webrtc-signal', {
        ...message,
        senderId: client.id,
      });
    } else {
      // Broadcast to room (excluding sender)
      client.to(clientInfo.roomId).emit('webrtc-signal', {
        ...message,
        senderId: client.id,
      });
    }
  }

  @SubscribeMessage('robot-control')
  handleRobotControl(
    @ConnectedSocket() client: Socket,
    @MessageBody() message: RobotControlMessage
  ) {
    this.logger.log(`Robot control from ${client.id}: ${message.type}`);
    
    const clientInfo = this.clients.get(client.id);
    if (!clientInfo?.roomId) {
      client.emit('error', { message: 'Not in a room' });
      return;
    }
    
    // Forward control message to all robots in the room
    client.to(clientInfo.roomId).emit('robot-control', {
      ...message,
      senderId: client.id,
    });
  }

  @SubscribeMessage('robot-status')
  handleRobotStatus(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { status: string; battery?: number; position?: any }
  ) {
    const clientInfo = this.clients.get(client.id);
    if (!clientInfo?.roomId) {
      return;
    }
    
    // Broadcast robot status to controllers in the room
    client.to(clientInfo.roomId).emit('robot-status', {
      ...data,
      robotId: client.id,
    });
  }

  private leaveRoom(client: Socket, roomId: string) {
    const room = this.rooms.get(roomId);
    if (room) {
      room.delete(client.id);
      if (room.size === 0) {
        this.rooms.delete(roomId);
      } else {
        // Notify others in the room
        client.to(roomId).emit('user-left', { clientId: client.id });
      }
    }
    
    client.leave(roomId);
    
    // Update client info
    const clientInfo = this.clients.get(client.id);
    if (clientInfo) {
      delete clientInfo.roomId;
    }
  }

  // Method to get room statistics (for monitoring)
  getRoomStats() {
    const stats = Array.from(this.rooms.entries()).map(([roomId, clients]) => ({
      roomId,
      clientCount: clients.size,
      clients: Array.from(clients),
    }));
    
    return {
      totalRooms: this.rooms.size,
      totalClients: this.clients.size,
      rooms: stats,
    };
  }
}
