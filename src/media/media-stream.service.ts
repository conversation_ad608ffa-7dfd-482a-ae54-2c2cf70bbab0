import { Injectable, Logger } from '@nestjs/common';

export interface MediaStreamConfig {
  video: {
    width: number;
    height: number;
    frameRate: number;
    bitrate: number;
    codec: 'H264' | 'VP8' | 'VP9';
  };
  audio: {
    sampleRate: number;
    channels: number;
    bitrate: number;
    codec: 'OPUS' | 'G722' | 'PCMU';
  };
}

export interface StreamQuality {
  level: 'low' | 'medium' | 'high' | 'ultra';
  config: MediaStreamConfig;
}

export interface MediaStreamStats {
  streamId: string;
  peerId: string;
  quality: string;
  bitrate: number;
  frameRate: number;
  packetsLost: number;
  jitter: number;
  latency: number;
  timestamp: Date;
}

@Injectable()
export class MediaStreamService {
  private readonly logger = new Logger(MediaStreamService.name);
  private activeStreams = new Map<string, MediaStreamStats>();
  
  // Predefined quality levels
  private readonly qualityLevels: Record<string, StreamQuality> = {
    low: {
      level: 'low',
      config: {
        video: {
          width: 640,
          height: 480,
          frameRate: 15,
          bitrate: 500000, // 500 kbps
          codec: 'H264',
        },
        audio: {
          sampleRate: 16000,
          channels: 1,
          bitrate: 32000, // 32 kbps
          codec: 'OPUS',
        },
      },
    },
    medium: {
      level: 'medium',
      config: {
        video: {
          width: 1280,
          height: 720,
          frameRate: 24,
          bitrate: 1500000, // 1.5 Mbps
          codec: 'H264',
        },
        audio: {
          sampleRate: 48000,
          channels: 2,
          bitrate: 64000, // 64 kbps
          codec: 'OPUS',
        },
      },
    },
    high: {
      level: 'high',
      config: {
        video: {
          width: 1920,
          height: 1080,
          frameRate: 30,
          bitrate: 3000000, // 3 Mbps
          codec: 'H264',
        },
        audio: {
          sampleRate: 48000,
          channels: 2,
          bitrate: 128000, // 128 kbps
          codec: 'OPUS',
        },
      },
    },
    ultra: {
      level: 'ultra',
      config: {
        video: {
          width: 3840,
          height: 2160,
          frameRate: 60,
          bitrate: 8000000, // 8 Mbps
          codec: 'H264',
        },
        audio: {
          sampleRate: 48000,
          channels: 2,
          bitrate: 256000, // 256 kbps
          codec: 'OPUS',
        },
      },
    },
  };

  /**
   * Get media configuration for a specific quality level
   */
  getMediaConfig(quality: string): MediaStreamConfig {
    const qualityLevel = this.qualityLevels[quality];
    if (!qualityLevel) {
      this.logger.warn(`Unknown quality level: ${quality}, using medium`);
      return this.qualityLevels.medium.config;
    }
    return qualityLevel.config;
  }

  /**
   * Generate WebRTC media constraints based on quality
   */
  generateMediaConstraints(quality: string): MediaStreamConstraints {
    const config = this.getMediaConfig(quality);
    
    return {
      video: {
        width: { ideal: config.video.width },
        height: { ideal: config.video.height },
        frameRate: { ideal: config.video.frameRate },
      },
      audio: {
        sampleRate: { ideal: config.audio.sampleRate },
        channelCount: { ideal: config.audio.channels },
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      },
    };
  }

  /**
   * Generate RTC configuration for peer connection
   */
  generateRTCConfiguration(): RTCConfiguration {
    return {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
        // Add TURN servers for production
        // {
        //   urls: 'turn:your-turn-server.com:3478',
        //   username: 'username',
        //   credential: 'password'
        // }
      ],
      iceCandidatePoolSize: 10,
    };
  }

  /**
   * Create optimized SDP for robot streaming
   */
  optimizeSDP(sdp: string, quality: string): string {
    const config = this.getMediaConfig(quality);
    let optimizedSDP = sdp;

    // Set video bitrate
    optimizedSDP = this.setSdpBitrate(optimizedSDP, 'video', config.video.bitrate);
    
    // Set audio bitrate
    optimizedSDP = this.setSdpBitrate(optimizedSDP, 'audio', config.audio.bitrate);
    
    // Set preferred codec
    optimizedSDP = this.setPreferredCodec(optimizedSDP, 'video', config.video.codec);
    optimizedSDP = this.setPreferredCodec(optimizedSDP, 'audio', config.audio.codec);

    return optimizedSDP;
  }

  /**
   * Register a new media stream
   */
  registerStream(streamId: string, peerId: string, quality: string): void {
    const stats: MediaStreamStats = {
      streamId,
      peerId,
      quality,
      bitrate: 0,
      frameRate: 0,
      packetsLost: 0,
      jitter: 0,
      latency: 0,
      timestamp: new Date(),
    };

    this.activeStreams.set(streamId, stats);
    this.logger.log(`Registered stream ${streamId} for peer ${peerId} with quality ${quality}`);
  }

  /**
   * Update stream statistics
   */
  updateStreamStats(streamId: string, stats: Partial<MediaStreamStats>): void {
    const existingStats = this.activeStreams.get(streamId);
    if (existingStats) {
      Object.assign(existingStats, stats, { timestamp: new Date() });
    }
  }

  /**
   * Remove a stream
   */
  removeStream(streamId: string): void {
    if (this.activeStreams.delete(streamId)) {
      this.logger.log(`Removed stream ${streamId}`);
    }
  }

  /**
   * Get stream statistics
   */
  getStreamStats(streamId: string): MediaStreamStats | undefined {
    return this.activeStreams.get(streamId);
  }

  /**
   * Get all active streams
   */
  getAllStreams(): MediaStreamStats[] {
    return Array.from(this.activeStreams.values());
  }

  /**
   * Get streams for a specific peer
   */
  getPeerStreams(peerId: string): MediaStreamStats[] {
    return Array.from(this.activeStreams.values()).filter(
      stream => stream.peerId === peerId
    );
  }

  /**
   * Adaptive quality adjustment based on network conditions
   */
  adjustQualityForNetwork(currentQuality: string, networkStats: {
    bandwidth: number;
    latency: number;
    packetLoss: number;
  }): string {
    const { bandwidth, latency, packetLoss } = networkStats;

    // Poor network conditions - downgrade quality
    if (packetLoss > 5 || latency > 200 || bandwidth < 1000000) {
      return 'low';
    }
    
    // Good network conditions - upgrade quality
    if (packetLoss < 1 && latency < 50 && bandwidth > 5000000) {
      return currentQuality === 'low' ? 'medium' : 
             currentQuality === 'medium' ? 'high' : 'ultra';
    }
    
    // Moderate network conditions
    if (packetLoss < 3 && latency < 100 && bandwidth > 2000000) {
      return currentQuality === 'low' ? 'medium' : currentQuality;
    }

    return currentQuality;
  }

  private setSdpBitrate(sdp: string, media: 'video' | 'audio', bitrate: number): string {
    const lines = sdp.split('\n');
    let mediaIndex = -1;

    // Find the media section
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith(`m=${media}`)) {
        mediaIndex = i;
        break;
      }
    }

    if (mediaIndex === -1) return sdp;

    // Add bitrate line after the media line
    const bitrateKbps = Math.floor(bitrate / 1000);
    const bitrateLine = `b=AS:${bitrateKbps}`;
    
    lines.splice(mediaIndex + 1, 0, bitrateLine);
    return lines.join('\n');
  }

  private setPreferredCodec(sdp: string, media: 'video' | 'audio', codec: string): string {
    // This is a simplified implementation
    // In production, you'd want more sophisticated SDP manipulation
    return sdp;
  }

  /**
   * Get service statistics
   */
  getServiceStats() {
    const streams = Array.from(this.activeStreams.values());
    const totalStreams = streams.length;
    const qualityDistribution = streams.reduce((acc, stream) => {
      acc[stream.quality] = (acc[stream.quality] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const avgBitrate = streams.length > 0 
      ? streams.reduce((sum, s) => sum + s.bitrate, 0) / streams.length 
      : 0;

    return {
      totalStreams,
      qualityDistribution,
      avgBitrate,
      availableQualities: Object.keys(this.qualityLevels),
    };
  }
}
