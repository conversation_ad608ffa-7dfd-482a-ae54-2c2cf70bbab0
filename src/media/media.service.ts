import { Injectable, Logger } from '@nestjs/common';

export interface StreamInfo {
  streamId: string;
  clientId: string;
  type: 'video' | 'audio' | 'screen' | 'mixed';
  quality: string;
  isActive: boolean;
  startTime: Date;
  stats?: StreamStats;
}

export interface StreamStats {
  bitrate: number;
  frameRate: number;
  resolution: { width: number; height: number };
  packetsLost: number;
  jitter: number;
  latency: number;
  lastUpdate: Date;
}

export interface MediaCapabilities {
  video: {
    codecs: string[];
    maxResolution: { width: number; height: number };
    maxFrameRate: number;
  };
  audio: {
    codecs: string[];
    maxSampleRate: number;
    maxChannels: number;
  };
}

@Injectable()
export class MediaService {
  private readonly logger = new Logger(MediaService.name);
  private activeStreams = new Map<string, StreamInfo>();
  private streamStats = new Map<string, StreamStats>();

  /**
   * 注册新的媒体流
   */
  registerStream(
    streamId: string,
    clientId: string,
    type: StreamInfo['type'],
    quality: string = 'medium',
  ): void {
    const streamInfo: StreamInfo = {
      streamId,
      clientId,
      type,
      quality,
      isActive: true,
      startTime: new Date(),
    };

    this.activeStreams.set(streamId, streamInfo);
    this.logger.log(`Stream registered: ${streamId} (${type}, ${quality})`);
  }

  /**
   * 注销媒体流
   */
  unregisterStream(streamId: string): void {
    const stream = this.activeStreams.get(streamId);
    if (stream) {
      stream.isActive = false;
      this.activeStreams.delete(streamId);
      this.streamStats.delete(streamId);
      this.logger.log(`Stream unregistered: ${streamId}`);
    }
  }

  /**
   * 更新流统计信息
   */
  updateStreamStats(streamId: string, stats: Partial<StreamStats>): void {
    const existingStats = this.streamStats.get(streamId);
    const updatedStats: StreamStats = {
      ...existingStats,
      ...stats,
      lastUpdate: new Date(),
    } as StreamStats;

    this.streamStats.set(streamId, updatedStats);

    // 更新流信息中的统计
    const stream = this.activeStreams.get(streamId);
    if (stream) {
      stream.stats = updatedStats;
    }
  }

  /**
   * 获取流信息
   */
  getStreamInfo(streamId: string): StreamInfo | undefined {
    return this.activeStreams.get(streamId);
  }

  /**
   * 获取客户端的所有流
   */
  getClientStreams(clientId: string): StreamInfo[] {
    return Array.from(this.activeStreams.values()).filter(
      (stream) => stream.clientId === clientId,
    );
  }

  /**
   * 获取所有活跃流
   */
  getAllActiveStreams(): StreamInfo[] {
    return Array.from(this.activeStreams.values()).filter(
      (stream) => stream.isActive,
    );
  }

  /**
   * 获取特定类型的流
   */
  getStreamsByType(type: StreamInfo['type']): StreamInfo[] {
    return Array.from(this.activeStreams.values()).filter(
      (stream) => stream.type === type && stream.isActive,
    );
  }

  /**
   * 获取媒体能力信息
   */
  getMediaCapabilities(): MediaCapabilities {
    return {
      video: {
        codecs: ['H264', 'VP8', 'VP9', 'AV1'],
        maxResolution: { width: 3840, height: 2160 },
        maxFrameRate: 60,
      },
      audio: {
        codecs: ['OPUS', 'G722', 'PCMU', 'PCMA'],
        maxSampleRate: 48000,
        maxChannels: 2,
      },
    };
  }

  /**
   * 检查编解码器支持
   */
  isCodecSupported(codec: string, type: 'video' | 'audio'): boolean {
    const capabilities = this.getMediaCapabilities();
    const supportedCodecs =
      type === 'video' ? capabilities.video.codecs : capabilities.audio.codecs;

    return supportedCodecs.includes(codec.toUpperCase());
  }

  /**
   * 获取推荐的编解码器
   */
  getRecommendedCodecs(): { video: string; audio: string } {
    return {
      video: 'H264', // 最广泛支持
      audio: 'OPUS', // 最佳质量和压缩比
    };
  }

  /**
   * 根据网络条件调整质量
   */
  adjustQualityForNetwork(
    currentQuality: string,
    networkConditions: {
      bandwidth: number; // bps
      latency: number; // ms
      packetLoss: number; // percentage
    },
  ): string {
    const { bandwidth, latency, packetLoss } = networkConditions;

    // 网络条件差 - 降低质量
    if (packetLoss > 5 || latency > 200 || bandwidth < 500000) {
      return 'low';
    }

    // 网络条件好 - 可以提高质量
    if (packetLoss < 1 && latency < 50 && bandwidth > 3000000) {
      return currentQuality === 'low' ? 'medium' : 'high';
    }

    // 网络条件中等
    if (packetLoss < 3 && latency < 100 && bandwidth > 1000000) {
      return currentQuality === 'low' ? 'medium' : currentQuality;
    }

    return currentQuality;
  }

  /**
   * 生成流统计报告
   */
  generateStreamReport(streamId: string): any {
    const stream = this.activeStreams.get(streamId);
    const stats = this.streamStats.get(streamId);

    if (!stream) {
      return null;
    }

    const duration = Date.now() - stream.startTime.getTime();

    return {
      streamId,
      clientId: stream.clientId,
      type: stream.type,
      quality: stream.quality,
      duration: Math.floor(duration / 1000), // seconds
      isActive: stream.isActive,
      stats: stats || null,
    };
  }

  /**
   * 获取服务统计信息
   */
  getServiceStats() {
    const allStreams = Array.from(this.activeStreams.values());
    const activeStreams = allStreams.filter((s) => s.isActive);

    const typeDistribution = activeStreams.reduce(
      (acc, stream) => {
        acc[stream.type] = (acc[stream.type] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const qualityDistribution = activeStreams.reduce(
      (acc, stream) => {
        acc[stream.quality] = (acc[stream.quality] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    // 计算平均统计
    const allStats = Array.from(this.streamStats.values());
    const avgBitrate =
      allStats.length > 0
        ? allStats.reduce((sum, s) => sum + (s.bitrate || 0), 0) /
          allStats.length
        : 0;

    return {
      totalStreams: allStreams.length,
      activeStreams: activeStreams.length,
      typeDistribution,
      qualityDistribution,
      avgBitrate: Math.round(avgBitrate),
      capabilities: this.getMediaCapabilities(),
    };
  }

  /**
   * 清理过期的流统计
   */
  cleanupExpiredStats(maxAgeMinutes: number = 60): number {
    const cutoffTime = new Date(Date.now() - maxAgeMinutes * 60 * 1000);
    let cleanedCount = 0;

    for (const [streamId, stats] of this.streamStats.entries()) {
      if (stats.lastUpdate < cutoffTime) {
        this.streamStats.delete(streamId);
        cleanedCount++;
      }
    }

    // 同时清理非活跃的流
    for (const [streamId, stream] of this.activeStreams.entries()) {
      if (!stream.isActive && stream.startTime < cutoffTime) {
        this.activeStreams.delete(streamId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.log(`Cleaned up ${cleanedCount} expired stream records`);
    }

    return cleanedCount;
  }

  /**
   * 验证流质量参数
   */
  validateQuality(quality: string): boolean {
    const validQualities = ['low', 'medium', 'high'];
    return validQualities.includes(quality);
  }

  /**
   * 获取质量建议
   */
  getQualityRecommendation(
    deviceType: 'mobile' | 'desktop' | 'tablet',
    networkType: 'wifi' | '4g' | '3g' | 'ethernet',
  ): string {
    // 基于设备类型和网络类型的质量建议
    if (deviceType === 'mobile' && networkType === '3g') {
      return 'low';
    }

    if (deviceType === 'mobile' && networkType === '4g') {
      return 'medium';
    }

    if (deviceType === 'desktop' && networkType === 'ethernet') {
      return 'high';
    }

    return 'medium'; // 默认推荐
  }
}
