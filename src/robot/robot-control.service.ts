import { Injectable, Logger } from '@nestjs/common';

export interface RobotPosition {
  x: number;
  y: number;
  z: number;
  orientation: {
    roll: number;
    pitch: number;
    yaw: number;
  };
}

export interface RobotStatus {
  id: string;
  isConnected: boolean;
  battery: number;
  position: RobotPosition;
  speed: number;
  temperature: number;
  lastUpdate: Date;
  errors: string[];
}

export interface MovementCommand {
  type: 'move' | 'rotate' | 'stop' | 'custom';
  direction?: 'forward' | 'backward' | 'left' | 'right' | 'up' | 'down';
  speed?: number; // 0-100
  duration?: number; // milliseconds
  angle?: number; // degrees for rotation
  customParams?: Record<string, any>;
}

export interface CameraCommand {
  type: 'pan' | 'tilt' | 'zoom' | 'focus' | 'preset';
  angle?: { x: number; y: number }; // degrees
  zoom?: number; // 0-100
  preset?: string;
}

export interface RobotCommand {
  id: string;
  robotId: string;
  timestamp: Date;
  type: 'movement' | 'camera' | 'system';
  command: MovementCommand | CameraCommand | any;
  priority: 'low' | 'normal' | 'high' | 'emergency';
  status: 'pending' | 'executing' | 'completed' | 'failed';
}

@Injectable()
export class RobotControlService {
  private readonly logger = new Logger(RobotControlService.name);
  private robots = new Map<string, RobotStatus>();
  private commandQueue = new Map<string, RobotCommand[]>(); // robotId -> commands
  private commandHistory = new Map<string, RobotCommand[]>(); // robotId -> history

  /**
   * Register a new robot
   */
  registerRobot(robotId: string): void {
    const robot: RobotStatus = {
      id: robotId,
      isConnected: true,
      battery: 100,
      position: {
        x: 0,
        y: 0,
        z: 0,
        orientation: { roll: 0, pitch: 0, yaw: 0 },
      },
      speed: 0,
      temperature: 25,
      lastUpdate: new Date(),
      errors: [],
    };

    this.robots.set(robotId, robot);
    this.commandQueue.set(robotId, []);
    this.commandHistory.set(robotId, []);
    
    this.logger.log(`Robot ${robotId} registered`);
  }

  /**
   * Unregister a robot
   */
  unregisterRobot(robotId: string): void {
    this.robots.delete(robotId);
    this.commandQueue.delete(robotId);
    // Keep history for analysis
    
    this.logger.log(`Robot ${robotId} unregistered`);
  }

  /**
   * Update robot status
   */
  updateRobotStatus(robotId: string, status: Partial<RobotStatus>): void {
    const robot = this.robots.get(robotId);
    if (robot) {
      Object.assign(robot, status, { lastUpdate: new Date() });
    }
  }

  /**
   * Get robot status
   */
  getRobotStatus(robotId: string): RobotStatus | undefined {
    return this.robots.get(robotId);
  }

  /**
   * Get all robots
   */
  getAllRobots(): RobotStatus[] {
    return Array.from(this.robots.values());
  }

  /**
   * Send movement command to robot
   */
  sendMovementCommand(
    robotId: string,
    command: MovementCommand,
    priority: RobotCommand['priority'] = 'normal'
  ): string {
    return this.queueCommand(robotId, 'movement', command, priority);
  }

  /**
   * Send camera command to robot
   */
  sendCameraCommand(
    robotId: string,
    command: CameraCommand,
    priority: RobotCommand['priority'] = 'normal'
  ): string {
    return this.queueCommand(robotId, 'camera', command, priority);
  }

  /**
   * Emergency stop for robot
   */
  emergencyStop(robotId: string): string {
    this.logger.warn(`Emergency stop for robot ${robotId}`);
    
    // Clear all pending commands
    const queue = this.commandQueue.get(robotId);
    if (queue) {
      queue.forEach(cmd => {
        if (cmd.status === 'pending') {
          cmd.status = 'failed';
        }
      });
      queue.length = 0;
    }

    // Send emergency stop command
    const stopCommand: MovementCommand = {
      type: 'stop',
    };

    return this.queueCommand(robotId, 'movement', stopCommand, 'emergency');
  }

  /**
   * Get next command for robot
   */
  getNextCommand(robotId: string): RobotCommand | undefined {
    const queue = this.commandQueue.get(robotId);
    if (!queue || queue.length === 0) return undefined;

    // Sort by priority and timestamp
    queue.sort((a, b) => {
      const priorityOrder = { emergency: 0, high: 1, normal: 2, low: 3 };
      const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return a.timestamp.getTime() - b.timestamp.getTime();
    });

    return queue.find(cmd => cmd.status === 'pending');
  }

  /**
   * Mark command as executing
   */
  markCommandExecuting(commandId: string): void {
    this.updateCommandStatus(commandId, 'executing');
  }

  /**
   * Mark command as completed
   */
  markCommandCompleted(commandId: string): void {
    this.updateCommandStatus(commandId, 'completed');
  }

  /**
   * Mark command as failed
   */
  markCommandFailed(commandId: string, error?: string): void {
    this.updateCommandStatus(commandId, 'failed');
    
    if (error) {
      // Find the command and add error info
      for (const [robotId, queue] of this.commandQueue.entries()) {
        const command = queue.find(cmd => cmd.id === commandId);
        if (command) {
          (command as any).error = error;
          break;
        }
      }
    }
  }

  /**
   * Get command history for robot
   */
  getCommandHistory(robotId: string, limit: number = 100): RobotCommand[] {
    const history = this.commandHistory.get(robotId) || [];
    return history.slice(-limit);
  }

  /**
   * Validate movement command
   */
  validateMovementCommand(command: MovementCommand): { valid: boolean; error?: string } {
    if (command.speed !== undefined && (command.speed < 0 || command.speed > 100)) {
      return { valid: false, error: 'Speed must be between 0 and 100' };
    }

    if (command.angle !== undefined && (command.angle < -360 || command.angle > 360)) {
      return { valid: false, error: 'Angle must be between -360 and 360 degrees' };
    }

    if (command.duration !== undefined && command.duration < 0) {
      return { valid: false, error: 'Duration must be positive' };
    }

    return { valid: true };
  }

  /**
   * Validate camera command
   */
  validateCameraCommand(command: CameraCommand): { valid: boolean; error?: string } {
    if (command.angle) {
      if (command.angle.x < -180 || command.angle.x > 180) {
        return { valid: false, error: 'Camera X angle must be between -180 and 180 degrees' };
      }
      if (command.angle.y < -90 || command.angle.y > 90) {
        return { valid: false, error: 'Camera Y angle must be between -90 and 90 degrees' };
      }
    }

    if (command.zoom !== undefined && (command.zoom < 0 || command.zoom > 100)) {
      return { valid: false, error: 'Zoom must be between 0 and 100' };
    }

    return { valid: true };
  }

  /**
   * Get robot control statistics
   */
  getControlStats() {
    const totalRobots = this.robots.size;
    const connectedRobots = Array.from(this.robots.values()).filter(r => r.isConnected).length;
    const totalCommands = Array.from(this.commandQueue.values()).reduce(
      (sum, queue) => sum + queue.length, 0
    );
    
    const robotStats = Array.from(this.robots.entries()).map(([id, robot]) => ({
      robotId: id,
      isConnected: robot.isConnected,
      battery: robot.battery,
      queuedCommands: this.commandQueue.get(id)?.length || 0,
      lastUpdate: robot.lastUpdate,
    }));

    return {
      totalRobots,
      connectedRobots,
      totalCommands,
      robots: robotStats,
    };
  }

  private queueCommand(
    robotId: string,
    type: RobotCommand['type'],
    command: any,
    priority: RobotCommand['priority']
  ): string {
    const robot = this.robots.get(robotId);
    if (!robot) {
      throw new Error(`Robot ${robotId} not found`);
    }

    if (!robot.isConnected) {
      throw new Error(`Robot ${robotId} is not connected`);
    }

    // Validate command based on type
    if (type === 'movement') {
      const validation = this.validateMovementCommand(command);
      if (!validation.valid) {
        throw new Error(validation.error);
      }
    } else if (type === 'camera') {
      const validation = this.validateCameraCommand(command);
      if (!validation.valid) {
        throw new Error(validation.error);
      }
    }

    const commandId = this.generateCommandId();
    const robotCommand: RobotCommand = {
      id: commandId,
      robotId,
      timestamp: new Date(),
      type,
      command,
      priority,
      status: 'pending',
    };

    const queue = this.commandQueue.get(robotId)!;
    queue.push(robotCommand);

    this.logger.log(`Queued ${type} command for robot ${robotId}: ${commandId}`);
    return commandId;
  }

  private updateCommandStatus(commandId: string, status: RobotCommand['status']): void {
    for (const [robotId, queue] of this.commandQueue.entries()) {
      const commandIndex = queue.findIndex(cmd => cmd.id === commandId);
      if (commandIndex !== -1) {
        const command = queue[commandIndex];
        command.status = status;

        // Move completed/failed commands to history
        if (status === 'completed' || status === 'failed') {
          queue.splice(commandIndex, 1);
          const history = this.commandHistory.get(robotId)!;
          history.push(command);
          
          // Keep only last 1000 commands in history
          if (history.length > 1000) {
            history.splice(0, history.length - 1000);
          }
        }
        break;
      }
    }
  }

  private generateCommandId(): string {
    return `cmd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
