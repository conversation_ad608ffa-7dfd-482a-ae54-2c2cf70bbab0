import { Injectable, Logger } from '@nestjs/common';

export interface DataChannelMessage {
  id: string;
  senderId: string;
  targetId?: string;
  type: 'text' | 'binary' | 'json' | 'file' | 'command';
  data: any;
  timestamp: Date;
  priority: 'low' | 'normal' | 'high';
}

export interface DataChannelConnection {
  connectionId: string;
  clientId: string;
  channelLabel: string;
  isOpen: boolean;
  createdAt: Date;
  lastActivity: Date;
  messageCount: number;
  bytesTransferred: number;
}

export interface FileTransfer {
  transferId: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  senderId: string;
  receiverId: string;
  chunks: ArrayBuffer[];
  totalChunks: number;
  receivedChunks: number;
  startTime: Date;
  status: 'pending' | 'transferring' | 'completed' | 'failed';
}

@Injectable()
export class DataChannelService {
  private readonly logger = new Logger(DataChannelService.name);
  private connections = new Map<string, DataChannelConnection>();
  private messageHistory = new Map<string, DataChannelMessage[]>();
  private fileTransfers = new Map<string, FileTransfer>();

  /**
   * 注册数据通道连接
   */
  registerConnection(
    connectionId: string,
    clientId: string,
    channelLabel: string = 'dataChannel',
  ): void {
    const connection: DataChannelConnection = {
      connectionId,
      clientId,
      channelLabel,
      isOpen: true,
      createdAt: new Date(),
      lastActivity: new Date(),
      messageCount: 0,
      bytesTransferred: 0,
    };

    this.connections.set(connectionId, connection);
    this.messageHistory.set(connectionId, []);

    this.logger.log(
      `Data channel registered: ${connectionId} for client ${clientId}`,
    );
  }

  /**
   * 注销数据通道连接
   */
  unregisterConnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.isOpen = false;
      this.connections.delete(connectionId);

      // 保留消息历史一段时间用于调试
      setTimeout(
        () => {
          this.messageHistory.delete(connectionId);
        },
        5 * 60 * 1000,
      ); // 5分钟后删除

      this.logger.log(`Data channel unregistered: ${connectionId}`);
    }
  }

  /**
   * 处理数据通道消息
   */
  handleMessage(
    connectionId: string,
    message: Omit<DataChannelMessage, 'id' | 'timestamp'>,
  ): DataChannelMessage {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      throw new Error(`Connection not found: ${connectionId}`);
    }

    const fullMessage: DataChannelMessage = {
      ...message,
      id: this.generateMessageId(),
      timestamp: new Date(),
    };

    // 更新连接统计
    connection.lastActivity = new Date();
    connection.messageCount++;

    // 估算数据大小
    const dataSize = this.estimateDataSize(fullMessage.data);
    connection.bytesTransferred += dataSize;

    // 保存消息历史
    const history = this.messageHistory.get(connectionId) || [];
    history.push(fullMessage);

    // 限制历史记录大小
    if (history.length > 1000) {
      history.splice(0, history.length - 1000);
    }

    this.logger.log(
      `Message handled: ${fullMessage.type} from ${fullMessage.senderId} (${dataSize} bytes)`,
    );

    return fullMessage;
  }

  /**
   * 开始文件传输
   */
  startFileTransfer(
    senderId: string,
    receiverId: string,
    fileName: string,
    fileSize: number,
    mimeType: string,
    totalChunks: number,
  ): string {
    const transferId = this.generateTransferId();

    const fileTransfer: FileTransfer = {
      transferId,
      fileName,
      fileSize,
      mimeType,
      senderId,
      receiverId,
      chunks: new Array(totalChunks),
      totalChunks,
      receivedChunks: 0,
      startTime: new Date(),
      status: 'pending',
    };

    this.fileTransfers.set(transferId, fileTransfer);

    this.logger.log(
      `File transfer started: ${fileName} (${fileSize} bytes, ${totalChunks} chunks)`,
    );

    return transferId;
  }

  /**
   * 处理文件块
   */
  handleFileChunk(
    transferId: string,
    chunkIndex: number,
    chunkData: ArrayBuffer,
  ): { completed: boolean; progress: number } {
    const transfer = this.fileTransfers.get(transferId);
    if (!transfer) {
      throw new Error(`File transfer not found: ${transferId}`);
    }

    if (transfer.status === 'completed' || transfer.status === 'failed') {
      throw new Error(`File transfer already ${transfer.status}`);
    }

    // 存储块数据
    transfer.chunks[chunkIndex] = chunkData;
    transfer.receivedChunks++;
    transfer.status = 'transferring';

    const progress = (transfer.receivedChunks / transfer.totalChunks) * 100;
    const completed = transfer.receivedChunks === transfer.totalChunks;

    if (completed) {
      transfer.status = 'completed';
      this.logger.log(`File transfer completed: ${transfer.fileName}`);
    }

    return { completed, progress };
  }

  /**
   * 获取完整文件数据
   */
  getCompleteFile(transferId: string): ArrayBuffer | null {
    const transfer = this.fileTransfers.get(transferId);
    if (!transfer || transfer.status !== 'completed') {
      return null;
    }

    // 合并所有块
    const totalSize = transfer.chunks.reduce(
      (sum, chunk) => sum + chunk.byteLength,
      0,
    );
    const completeFile = new ArrayBuffer(totalSize);
    const view = new Uint8Array(completeFile);

    let offset = 0;
    for (const chunk of transfer.chunks) {
      view.set(new Uint8Array(chunk), offset);
      offset += chunk.byteLength;
    }

    return completeFile;
  }

  /**
   * 取消文件传输
   */
  cancelFileTransfer(transferId: string): void {
    const transfer = this.fileTransfers.get(transferId);
    if (transfer) {
      transfer.status = 'failed';
      this.logger.log(`File transfer cancelled: ${transfer.fileName}`);
    }
  }

  /**
   * 获取连接信息
   */
  getConnection(connectionId: string): DataChannelConnection | undefined {
    return this.connections.get(connectionId);
  }

  /**
   * 获取客户端的所有连接
   */
  getClientConnections(clientId: string): DataChannelConnection[] {
    return Array.from(this.connections.values()).filter(
      (conn) => conn.clientId === clientId,
    );
  }

  /**
   * 获取消息历史
   */
  getMessageHistory(
    connectionId: string,
    limit: number = 100,
  ): DataChannelMessage[] {
    const history = this.messageHistory.get(connectionId) || [];
    return history.slice(-limit);
  }

  /**
   * 获取文件传输状态
   */
  getFileTransferStatus(transferId: string): FileTransfer | undefined {
    return this.fileTransfers.get(transferId);
  }

  /**
   * 获取活跃的文件传输
   */
  getActiveFileTransfers(): FileTransfer[] {
    return Array.from(this.fileTransfers.values()).filter(
      (transfer) =>
        transfer.status === 'pending' || transfer.status === 'transferring',
    );
  }

  /**
   * 清理过期的传输
   */
  cleanupExpiredTransfers(maxAgeMinutes: number = 60): number {
    const cutoffTime = new Date(Date.now() - maxAgeMinutes * 60 * 1000);
    let cleanedCount = 0;

    for (const [transferId, transfer] of this.fileTransfers.entries()) {
      if (
        transfer.startTime < cutoffTime &&
        (transfer.status === 'completed' || transfer.status === 'failed')
      ) {
        this.fileTransfers.delete(transferId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.log(`Cleaned up ${cleanedCount} expired file transfers`);
    }

    return cleanedCount;
  }

  /**
   * 获取服务统计
   */
  getServiceStats() {
    const allConnections = Array.from(this.connections.values());
    const activeConnections = allConnections.filter((conn) => conn.isOpen);
    const totalMessages = allConnections.reduce(
      (sum, conn) => sum + conn.messageCount,
      0,
    );
    const totalBytes = allConnections.reduce(
      (sum, conn) => sum + conn.bytesTransferred,
      0,
    );

    const allTransfers = Array.from(this.fileTransfers.values());
    const activeTransfers = allTransfers.filter(
      (t) => t.status === 'pending' || t.status === 'transferring',
    );
    const completedTransfers = allTransfers.filter(
      (t) => t.status === 'completed',
    );

    return {
      connections: {
        total: allConnections.length,
        active: activeConnections.length,
      },
      messages: {
        total: totalMessages,
        totalBytes,
      },
      fileTransfers: {
        total: allTransfers.length,
        active: activeTransfers.length,
        completed: completedTransfers.length,
      },
    };
  }

  /**
   * 验证消息格式
   */
  validateMessage(message: any): boolean {
    return (
      message &&
      typeof message.senderId === 'string' &&
      typeof message.type === 'string' &&
      ['text', 'binary', 'json', 'file', 'command'].includes(message.type) &&
      message.data !== undefined
    );
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateTransferId(): string {
    return `transfer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private estimateDataSize(data: any): number {
    if (data instanceof ArrayBuffer) {
      return data.byteLength;
    }
    if (typeof data === 'string') {
      return new Blob([data]).size;
    }
    if (typeof data === 'object') {
      return new Blob([JSON.stringify(data)]).size;
    }
    return 0;
  }
}
