import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  const logger = new Logger('Bootstrap');

  // Serve static files
  app.useStaticAssets(join(__dirname, '..', 'public'));

  // Enable CORS for WebRTC
  app.enableCors({
    origin: [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:8080',
      'https://localhost:3000',
      'https://localhost:3001',
      'https://localhost:8080',
      // Add your production domains here
    ],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
  });

  // Set global prefix for API routes
  app.setGlobalPrefix('api');

  const port = process.env.PORT || 3000;
  await app.listen(port);

  logger.log(
    `🚀 Robot Dog WebRTC Server is running on: http://localhost:${port}/api`,
  );
  logger.log(`📡 WebSocket endpoint: ws://localhost:${port}/webrtc`);
  logger.log(`🔧 Health check: http://localhost:${port}/api/webrtc/health`);
  logger.log(`🧪 Test client: http://localhost:${port}/test-client.html`);
}
bootstrap();
