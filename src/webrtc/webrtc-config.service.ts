import { Injectable, Logger } from '@nestjs/common';

export interface ICEServer {
  urls: string | string[];
  username?: string;
  credential?: string;
}

export interface WebRTCConfiguration {
  iceServers: ICEServer[];
  iceCandidatePoolSize?: number;
  iceTransportPolicy?: 'all' | 'relay';
  bundlePolicy?: 'balanced' | 'max-compat' | 'max-bundle';
  rtcpMuxPolicy?: 'negotiate' | 'require';
}

export interface MediaConstraints {
  video: MediaTrackConstraints | boolean;
  audio: MediaTrackConstraints | boolean;
}

export interface StreamQuality {
  video: {
    width: number;
    height: number;
    frameRate: number;
    bitrate: number;
  };
  audio: {
    sampleRate: number;
    channels: number;
    bitrate: number;
  };
}

@Injectable()
export class WebRTCConfigService {
  private readonly logger = new Logger(WebRTCConfigService.name);

  // 预定义的质量级别
  private readonly qualityPresets: Record<string, StreamQuality> = {
    low: {
      video: { width: 640, height: 480, frameRate: 15, bitrate: 500000 },
      audio: { sampleRate: 16000, channels: 1, bitrate: 32000 },
    },
    medium: {
      video: { width: 1280, height: 720, frameRate: 24, bitrate: 1500000 },
      audio: { sampleRate: 48000, channels: 2, bitrate: 64000 },
    },
    high: {
      video: { width: 1920, height: 1080, frameRate: 30, bitrate: 3000000 },
      audio: { sampleRate: 48000, channels: 2, bitrate: 128000 },
    },
  };

  /**
   * 获取 WebRTC 配置
   */
  getWebRTCConfiguration(): WebRTCConfiguration {
    return {
      iceServers: this.getICEServers(),
      iceCandidatePoolSize: 10,
      iceTransportPolicy: 'all',
      bundlePolicy: 'max-bundle',
      rtcpMuxPolicy: 'require',
    };
  }

  /**
   * 获取 ICE 服务器配置
   */
  private getICEServers(): ICEServer[] {
    const servers: ICEServer[] = [
      // Google 公共 STUN 服务器
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      { urls: 'stun:stun2.l.google.com:19302' },
      { urls: 'stun:stun3.l.google.com:19302' },
      { urls: 'stun:stun4.l.google.com:19302' },
    ];

    // 从环境变量读取 TURN 服务器配置
    const turnUrl = process.env.TURN_URL;
    const turnUsername = process.env.TURN_USERNAME;
    const turnCredential = process.env.TURN_CREDENTIAL;

    if (turnUrl && turnUsername && turnCredential) {
      servers.push({
        urls: turnUrl,
        username: turnUsername,
        credential: turnCredential,
      });
      this.logger.log('TURN server configured');
    } else {
      this.logger.warn('TURN server not configured, using STUN only');
    }

    return servers;
  }

  /**
   * 获取媒体约束
   */
  getMediaConstraints(quality: string = 'medium'): MediaConstraints {
    const preset = this.qualityPresets[quality] || this.qualityPresets.medium;

    return {
      video: {
        width: { ideal: preset.video.width },
        height: { ideal: preset.video.height },
        frameRate: { ideal: preset.video.frameRate },
      },
      audio: {
        sampleRate: { ideal: preset.audio.sampleRate },
        channelCount: { ideal: preset.audio.channels },
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      },
    };
  }

  /**
   * 获取仅音频约束
   */
  getAudioOnlyConstraints(): MediaConstraints {
    return {
      video: false,
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      },
    };
  }

  /**
   * 获取仅视频约束
   */
  getVideoOnlyConstraints(quality: string = 'medium'): MediaConstraints {
    const preset = this.qualityPresets[quality] || this.qualityPresets.medium;

    return {
      video: {
        width: { ideal: preset.video.width },
        height: { ideal: preset.video.height },
        frameRate: { ideal: preset.video.frameRate },
      },
      audio: false,
    };
  }

  /**
   * 获取屏幕共享约束
   */
  getScreenShareConstraints(): MediaConstraints {
    return {
      video: {
        width: { ideal: 1920 },
        height: { ideal: 1080 },
        frameRate: { ideal: 30 },
      },
      audio: true,
    };
  }

  /**
   * 获取数据通道配置
   */
  getDataChannelConfig(label: string = 'dataChannel') {
    return {
      label,
      ordered: true,
      maxRetransmits: 3,
    };
  }

  /**
   * 获取可用的质量级别
   */
  getAvailableQualities(): string[] {
    return Object.keys(this.qualityPresets);
  }

  /**
   * 获取质量级别详情
   */
  getQualityDetails(quality: string): StreamQuality | null {
    return this.qualityPresets[quality] || null;
  }

  /**
   * 优化 SDP
   */
  optimizeSDP(sdp: string, quality: string = 'medium'): string {
    const preset = this.qualityPresets[quality] || this.qualityPresets.medium;
    let optimizedSDP = sdp;

    // 设置视频码率
    optimizedSDP = this.setSdpBitrate(
      optimizedSDP,
      'video',
      preset.video.bitrate,
    );

    // 设置音频码率
    optimizedSDP = this.setSdpBitrate(
      optimizedSDP,
      'audio',
      preset.audio.bitrate,
    );

    return optimizedSDP;
  }

  private setSdpBitrate(
    sdp: string,
    media: 'video' | 'audio',
    bitrate: number,
  ): string {
    const lines = sdp.split('\n');
    let mediaIndex = -1;

    // 找到媒体段
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith(`m=${media}`)) {
        mediaIndex = i;
        break;
      }
    }

    if (mediaIndex === -1) return sdp;

    // 添加码率限制
    const bitrateKbps = Math.floor(bitrate / 1000);
    const bitrateLine = `b=AS:${bitrateKbps}`;

    // 检查是否已存在码率设置
    let insertIndex = mediaIndex + 1;
    while (insertIndex < lines.length && !lines[insertIndex].startsWith('m=')) {
      if (lines[insertIndex].startsWith('b=AS:')) {
        lines[insertIndex] = bitrateLine;
        return lines.join('\n');
      }
      insertIndex++;
    }

    // 插入新的码率设置
    lines.splice(mediaIndex + 1, 0, bitrateLine);
    return lines.join('\n');
  }

  /**
   * 验证 WebRTC 支持
   */
  checkWebRTCSupport(): {
    supported: boolean;
    features: {
      getUserMedia: boolean;
      RTCPeerConnection: boolean;
      RTCDataChannel: boolean;
    };
  } {
    // 这个方法主要用于客户端，服务端总是返回支持状态
    return {
      supported: true,
      features: {
        getUserMedia: true,
        RTCPeerConnection: true,
        RTCDataChannel: true,
      },
    };
  }
}
