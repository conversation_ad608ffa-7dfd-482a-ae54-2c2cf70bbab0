import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { SignalingService } from './signaling.service';
import { MediaStreamService } from '../media/media-stream.service';
import { RobotControlService, MovementCommand, CameraCommand } from '../robot/robot-control.service';

@Controller('webrtc')
export class WebRTCController {
  constructor(
    private readonly signalingService: SignalingService,
    private readonly mediaStreamService: MediaStreamService,
    private readonly robotControlService: RobotControlService,
  ) {}

  /**
   * Get WebRTC configuration for clients
   */
  @Get('config')
  getWebRTCConfig(@Query('quality') quality: string = 'medium') {
    const mediaConfig = this.mediaStreamService.getMediaConfig(quality);
    const rtcConfig = this.mediaStreamService.generateRTCConfiguration();
    const mediaConstraints = this.mediaStreamService.generateMediaConstraints(quality);

    return {
      rtcConfiguration: rtcConfig,
      mediaConstraints,
      mediaConfig,
      availableQualities: ['low', 'medium', 'high', 'ultra'],
    };
  }

  /**
   * Get signaling service statistics
   */
  @Get('stats/signaling')
  getSignalingStats() {
    return this.signalingService.getStats();
  }

  /**
   * Get media stream statistics
   */
  @Get('stats/media')
  getMediaStats() {
    return this.mediaStreamService.getServiceStats();
  }

  /**
   * Get robot control statistics
   */
  @Get('stats/robots')
  getRobotStats() {
    return this.robotControlService.getControlStats();
  }

  /**
   * Get all active rooms
   */
  @Get('rooms')
  getActiveRooms() {
    return this.signalingService.getActiveRooms();
  }

  /**
   * Get peers in a specific room
   */
  @Get('rooms/:roomId/peers')
  getRoomPeers(@Param('roomId') roomId: string) {
    return this.signalingService.getRoomPeers(roomId);
  }

  /**
   * Get all robots
   */
  @Get('robots')
  getAllRobots() {
    return this.robotControlService.getAllRobots();
  }

  /**
   * Get specific robot status
   */
  @Get('robots/:robotId')
  getRobotStatus(@Param('robotId') robotId: string) {
    const robot = this.robotControlService.getRobotStatus(robotId);
    if (!robot) {
      throw new HttpException('Robot not found', HttpStatus.NOT_FOUND);
    }
    return robot;
  }

  /**
   * Send movement command to robot
   */
  @Post('robots/:robotId/move')
  sendMovementCommand(
    @Param('robotId') robotId: string,
    @Body() body: { command: MovementCommand; priority?: 'low' | 'normal' | 'high' | 'emergency' }
  ) {
    try {
      const commandId = this.robotControlService.sendMovementCommand(
        robotId,
        body.command,
        body.priority || 'normal'
      );
      return { success: true, commandId };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Send camera command to robot
   */
  @Post('robots/:robotId/camera')
  sendCameraCommand(
    @Param('robotId') robotId: string,
    @Body() body: { command: CameraCommand; priority?: 'low' | 'normal' | 'high' | 'emergency' }
  ) {
    try {
      const commandId = this.robotControlService.sendCameraCommand(
        robotId,
        body.command,
        body.priority || 'normal'
      );
      return { success: true, commandId };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Emergency stop for robot
   */
  @Post('robots/:robotId/emergency-stop')
  emergencyStop(@Param('robotId') robotId: string) {
    try {
      const commandId = this.robotControlService.emergencyStop(robotId);
      return { success: true, commandId };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Get robot command history
   */
  @Get('robots/:robotId/commands')
  getRobotCommands(
    @Param('robotId') robotId: string,
    @Query('limit') limit: string = '100'
  ) {
    const limitNum = parseInt(limit, 10);
    return this.robotControlService.getCommandHistory(robotId, limitNum);
  }

  /**
   * Get next command for robot
   */
  @Get('robots/:robotId/next-command')
  getNextCommand(@Param('robotId') robotId: string) {
    const command = this.robotControlService.getNextCommand(robotId);
    return command || { message: 'No pending commands' };
  }

  /**
   * Update command status (for robot clients)
   */
  @Post('commands/:commandId/status')
  updateCommandStatus(
    @Param('commandId') commandId: string,
    @Body() body: { status: 'executing' | 'completed' | 'failed'; error?: string }
  ) {
    try {
      switch (body.status) {
        case 'executing':
          this.robotControlService.markCommandExecuting(commandId);
          break;
        case 'completed':
          this.robotControlService.markCommandCompleted(commandId);
          break;
        case 'failed':
          this.robotControlService.markCommandFailed(commandId, body.error);
          break;
      }
      return { success: true };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Register a new robot
   */
  @Post('robots/:robotId/register')
  registerRobot(@Param('robotId') robotId: string) {
    try {
      this.robotControlService.registerRobot(robotId);
      return { success: true, message: `Robot ${robotId} registered` };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Update robot status
   */
  @Post('robots/:robotId/status')
  updateRobotStatus(
    @Param('robotId') robotId: string,
    @Body() status: any
  ) {
    try {
      this.robotControlService.updateRobotStatus(robotId, status);
      return { success: true };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Get media stream info
   */
  @Get('streams')
  getAllStreams() {
    return this.mediaStreamService.getAllStreams();
  }

  /**
   * Get streams for a specific peer
   */
  @Get('peers/:peerId/streams')
  getPeerStreams(@Param('peerId') peerId: string) {
    return this.mediaStreamService.getPeerStreams(peerId);
  }

  /**
   * Register a new media stream
   */
  @Post('streams/:streamId/register')
  registerStream(
    @Param('streamId') streamId: string,
    @Body() body: { peerId: string; quality: string }
  ) {
    try {
      this.mediaStreamService.registerStream(streamId, body.peerId, body.quality);
      return { success: true };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Update stream statistics
   */
  @Post('streams/:streamId/stats')
  updateStreamStats(
    @Param('streamId') streamId: string,
    @Body() stats: any
  ) {
    try {
      this.mediaStreamService.updateStreamStats(streamId, stats);
      return { success: true };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Health check endpoint
   */
  @Get('health')
  healthCheck() {
    const signalingStats = this.signalingService.getStats();
    const mediaStats = this.mediaStreamService.getServiceStats();
    const robotStats = this.robotControlService.getControlStats();

    return {
      status: 'healthy',
      timestamp: new Date(),
      services: {
        signaling: {
          totalPeers: signalingStats.totalPeers,
          totalRooms: signalingStats.totalRooms,
          connectedPeers: signalingStats.connectedPeers,
        },
        media: {
          totalStreams: mediaStats.totalStreams,
          avgBitrate: mediaStats.avgBitrate,
        },
        robots: {
          totalRobots: robotStats.totalRobots,
          connectedRobots: robotStats.connectedRobots,
          totalCommands: robotStats.totalCommands,
        },
      },
    };
  }
}
