import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { WebRTCConfigService } from './webrtc-config.service';
import { MediaService } from '../media/media.service';
import { DataChannelService } from '../datachannel/datachannel.service';
import { SignalingGateway } from '../signaling/signaling.gateway';

@Controller('webrtc')
export class WebRTCController {
  constructor(
    private readonly configService: WebRTCConfigService,
    private readonly mediaService: MediaService,
    private readonly dataChannelService: DataChannelService,
    private readonly signalingGateway: SignalingGateway,
  ) {}

  /**
   * 获取 WebRTC 配置
   */
  @Get('config')
  getConfig(@Query('quality') quality: string = 'medium') {
    const rtcConfig = this.configService.getWebRTCConfiguration();
    const mediaConstraints = this.configService.getMediaConstraints(quality);
    const dataChannelConfig = this.configService.getDataChannelConfig();

    return {
      rtcConfiguration: rtcConfig,
      mediaConstraints,
      dataChannelConfig,
      availableQualities: this.configService.getAvailableQualities(),
      qualityDetails: this.configService.getQualityDetails(quality),
      mediaCapabilities: this.mediaService.getMediaCapabilities(),
    };
  }

  /**
   * 获取音频专用配置
   */
  @Get('config/audio-only')
  getAudioOnlyConfig() {
    const rtcConfig = this.configService.getWebRTCConfiguration();
    const mediaConstraints = this.configService.getAudioOnlyConstraints();

    return {
      rtcConfiguration: rtcConfig,
      mediaConstraints,
    };
  }

  /**
   * 获取屏幕共享配置
   */
  @Get('config/screen-share')
  getScreenShareConfig() {
    const rtcConfig = this.configService.getWebRTCConfiguration();
    const mediaConstraints = this.configService.getScreenShareConstraints();

    return {
      rtcConfiguration: rtcConfig,
      mediaConstraints,
    };
  }

  /**
   * 优化 SDP
   */
  @Post('optimize-sdp')
  optimizeSDP(@Body() body: { sdp: string; quality?: string }) {
    const { sdp, quality = 'medium' } = body;

    if (!sdp) {
      throw new HttpException('SDP is required', HttpStatus.BAD_REQUEST);
    }

    const optimizedSDP = this.configService.optimizeSDP(sdp, quality);

    return {
      originalSDP: sdp,
      optimizedSDP,
      quality,
    };
  }

  /**
   * 注册媒体流
   */
  @Post('streams/register')
  registerStream(
    @Body()
    body: {
      streamId: string;
      clientId: string;
      type: 'video' | 'audio' | 'screen' | 'mixed';
      quality?: string;
    },
  ) {
    const { streamId, clientId, type, quality = 'medium' } = body;

    this.mediaService.registerStream(streamId, clientId, type, quality);

    return { success: true, streamId };
  }

  /**
   * 更新流统计
   */
  @Post('streams/:streamId/stats')
  updateStreamStats(@Param('streamId') streamId: string, @Body() stats: any) {
    this.mediaService.updateStreamStats(streamId, stats);
    return { success: true };
  }

  /**
   * 获取流信息
   */
  @Get('streams/:streamId')
  getStreamInfo(@Param('streamId') streamId: string) {
    const stream = this.mediaService.getStreamInfo(streamId);
    if (!stream) {
      throw new HttpException('Stream not found', HttpStatus.NOT_FOUND);
    }
    return stream;
  }

  /**
   * 获取客户端的流
   */
  @Get('clients/:clientId/streams')
  getClientStreams(@Param('clientId') clientId: string) {
    return this.mediaService.getClientStreams(clientId);
  }

  /**
   * 注册数据通道
   */
  @Post('data-channels/register')
  registerDataChannel(
    @Body()
    body: {
      connectionId: string;
      clientId: string;
      channelLabel?: string;
    },
  ) {
    const { connectionId, clientId, channelLabel } = body;

    this.dataChannelService.registerConnection(
      connectionId,
      clientId,
      channelLabel,
    );

    return { success: true, connectionId };
  }

  /**
   * 获取数据通道连接
   */
  @Get('data-channels/:connectionId')
  getDataChannelConnection(@Param('connectionId') connectionId: string) {
    const connection = this.dataChannelService.getConnection(connectionId);
    if (!connection) {
      throw new HttpException('Connection not found', HttpStatus.NOT_FOUND);
    }
    return connection;
  }

  /**
   * 获取消息历史
   */
  @Get('data-channels/:connectionId/messages')
  getMessageHistory(
    @Param('connectionId') connectionId: string,
    @Query('limit') limit: string = '100',
  ) {
    const limitNum = parseInt(limit, 10);
    return this.dataChannelService.getMessageHistory(connectionId, limitNum);
  }

  /**
   * 开始文件传输
   */
  @Post('file-transfers/start')
  startFileTransfer(
    @Body()
    body: {
      senderId: string;
      receiverId: string;
      fileName: string;
      fileSize: number;
      mimeType: string;
      totalChunks: number;
    },
  ) {
    const { senderId, receiverId, fileName, fileSize, mimeType, totalChunks } =
      body;

    const transferId = this.dataChannelService.startFileTransfer(
      senderId,
      receiverId,
      fileName,
      fileSize,
      mimeType,
      totalChunks,
    );

    return { success: true, transferId };
  }

  /**
   * 获取文件传输状态
   */
  @Get('file-transfers/:transferId')
  getFileTransferStatus(@Param('transferId') transferId: string) {
    const transfer = this.dataChannelService.getFileTransferStatus(transferId);
    if (!transfer) {
      throw new HttpException('Transfer not found', HttpStatus.NOT_FOUND);
    }
    return transfer;
  }

  /**
   * 获取质量建议
   */
  @Post('quality-recommendation')
  getQualityRecommendation(
    @Body()
    body: {
      deviceType: 'mobile' | 'desktop' | 'tablet';
      networkType: 'wifi' | '4g' | '3g' | 'ethernet';
    },
  ) {
    const { deviceType, networkType } = body;
    const recommendation = this.mediaService.getQualityRecommendation(
      deviceType,
      networkType,
    );

    return {
      recommendedQuality: recommendation,
      qualityDetails: this.configService.getQualityDetails(recommendation),
    };
  }

  /**
   * 调整网络质量
   */
  @Post('adjust-quality')
  adjustQualityForNetwork(
    @Body()
    body: {
      currentQuality: string;
      networkConditions: {
        bandwidth: number;
        latency: number;
        packetLoss: number;
      };
    },
  ) {
    const { currentQuality, networkConditions } = body;
    const adjustedQuality = this.mediaService.adjustQualityForNetwork(
      currentQuality,
      networkConditions,
    );

    return {
      currentQuality,
      adjustedQuality,
      qualityDetails: this.configService.getQualityDetails(adjustedQuality),
      networkConditions,
    };
  }

  /**
   * 获取信令统计
   */
  @Get('stats/signaling')
  getSignalingStats() {
    return this.signalingGateway.getStats();
  }

  /**
   * 获取媒体统计
   */
  @Get('stats/media')
  getMediaStats() {
    return this.mediaService.getServiceStats();
  }

  /**
   * 获取数据通道统计
   */
  @Get('stats/data-channels')
  getDataChannelStats() {
    return this.dataChannelService.getServiceStats();
  }

  /**
   * 健康检查
   */
  @Get('health')
  healthCheck() {
    const signalingStats = this.signalingGateway.getStats();
    const mediaStats = this.mediaService.getServiceStats();
    const dataChannelStats = this.dataChannelService.getServiceStats();

    return {
      status: 'healthy',
      timestamp: new Date(),
      services: {
        signaling: {
          totalClients: signalingStats.totalClients,
          readyClients: signalingStats.readyClients,
        },
        media: {
          totalStreams: mediaStats.totalStreams,
          activeStreams: mediaStats.activeStreams,
          avgBitrate: mediaStats.avgBitrate,
        },
        dataChannels: {
          totalConnections: dataChannelStats.connections.total,
          activeConnections: dataChannelStats.connections.active,
          totalMessages: dataChannelStats.messages.total,
          activeFileTransfers: dataChannelStats.fileTransfers.active,
        },
      },
      capabilities: this.mediaService.getMediaCapabilities(),
    };
  }

  /**
   * 检查 WebRTC 支持
   */
  @Get('support-check')
  checkWebRTCSupport() {
    return this.configService.checkWebRTCSupport();
  }
}
