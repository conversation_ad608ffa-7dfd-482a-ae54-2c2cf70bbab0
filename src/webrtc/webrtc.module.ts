import { Modu<PERSON> } from '@nestjs/common';
import { SignalingService } from './signaling.service';
import { MediaStreamService } from '../media/media-stream.service';
import { RobotControlService } from '../robot/robot-control.service';
import { WebSocketGateway } from '../websocket/websocket.gateway';
import { WebRTCController } from './webrtc.controller';

@Module({
  providers: [
    SignalingService,
    MediaStreamService,
    RobotControlService,
    WebSocketGateway,
  ],
  controllers: [WebRTCController],
  exports: [
    SignalingService,
    MediaStreamService,
    RobotControlService,
    WebSocketGateway,
  ],
})
export class WebRTCModule {}
