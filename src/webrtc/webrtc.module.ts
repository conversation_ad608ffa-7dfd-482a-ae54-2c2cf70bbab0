import { Module } from '@nestjs/common';
import { WebRTCConfigService } from './webrtc-config.service';
import { WebRTCController } from './webrtc.controller';
import { SignalingGateway } from '../signaling/signaling.gateway';
import { MediaService } from '../media/media.service';
import { DataChannelService } from '../datachannel/datachannel.service';

@Module({
  providers: [
    WebRTCConfigService,
    SignalingGateway,
    MediaService,
    DataChannelService,
  ],
  controllers: [WebRTCController],
  exports: [
    WebRTCConfigService,
    SignalingGateway,
    MediaService,
    DataChannelService,
  ],
})
export class WebRTCModule {}
