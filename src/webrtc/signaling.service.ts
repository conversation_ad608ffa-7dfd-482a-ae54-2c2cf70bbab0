import { Injectable, Logger } from '@nestjs/common';

export interface RTCSessionDescriptionInit {
  type: 'offer' | 'answer';
  sdp: string;
}

export interface RTCIceCandidateInit {
  candidate: string;
  sdpMLineIndex?: number | null;
  sdpMid?: string | null;
}

export interface PeerConnection {
  id: string;
  roomId: string;
  clientType: 'robot' | 'controller';
  isConnected: boolean;
  lastActivity: Date;
}

export interface SignalingMessage {
  type: 'offer' | 'answer' | 'ice-candidate' | 'connection-state';
  senderId: string;
  targetId?: string;
  data: RTCSessionDescriptionInit | RTCIceCandidateInit | any;
}

@Injectable()
export class SignalingService {
  private readonly logger = new Logger(SignalingService.name);
  private peers = new Map<string, PeerConnection>();
  private roomConnections = new Map<string, Set<string>>(); // roomId -> Set of peerIds

  /**
   * Register a new peer connection
   */
  registerPeer(peerId: string, roomId: string, clientType: 'robot' | 'controller'): void {
    this.logger.log(`Registering peer ${peerId} in room ${roomId} as ${clientType}`);
    
    const peer: PeerConnection = {
      id: peerId,
      roomId,
      clientType,
      isConnected: false,
      lastActivity: new Date(),
    };
    
    this.peers.set(peerId, peer);
    
    // Add to room connections
    if (!this.roomConnections.has(roomId)) {
      this.roomConnections.set(roomId, new Set());
    }
    this.roomConnections.get(roomId)!.add(peerId);
  }

  /**
   * Unregister a peer connection
   */
  unregisterPeer(peerId: string): void {
    const peer = this.peers.get(peerId);
    if (peer) {
      this.logger.log(`Unregistering peer ${peerId} from room ${peer.roomId}`);
      
      // Remove from room connections
      const roomPeers = this.roomConnections.get(peer.roomId);
      if (roomPeers) {
        roomPeers.delete(peerId);
        if (roomPeers.size === 0) {
          this.roomConnections.delete(peer.roomId);
        }
      }
      
      this.peers.delete(peerId);
    }
  }

  /**
   * Process signaling message
   */
  processSignalingMessage(message: SignalingMessage): {
    targetPeers: string[];
    processedMessage: SignalingMessage;
  } {
    const sender = this.peers.get(message.senderId);
    if (!sender) {
      throw new Error(`Unknown sender: ${message.senderId}`);
    }

    // Update last activity
    sender.lastActivity = new Date();

    let targetPeers: string[] = [];

    if (message.targetId) {
      // Direct message to specific peer
      const target = this.peers.get(message.targetId);
      if (target && target.roomId === sender.roomId) {
        targetPeers = [message.targetId];
      }
    } else {
      // Broadcast to all peers in the same room (excluding sender)
      const roomPeers = this.roomConnections.get(sender.roomId);
      if (roomPeers) {
        targetPeers = Array.from(roomPeers).filter(id => id !== message.senderId);
      }
    }

    const processedMessage: SignalingMessage = {
      ...message,
      senderId: message.senderId,
    };

    // Handle specific message types
    switch (message.type) {
      case 'offer':
        this.handleOffer(sender, message.data as RTCSessionDescriptionInit);
        break;
      case 'answer':
        this.handleAnswer(sender, message.data as RTCSessionDescriptionInit);
        break;
      case 'ice-candidate':
        this.handleIceCandidate(sender, message.data as RTCIceCandidateInit);
        break;
      case 'connection-state':
        this.handleConnectionState(sender, message.data);
        break;
    }

    return { targetPeers, processedMessage };
  }

  /**
   * Get peers in a room
   */
  getRoomPeers(roomId: string): PeerConnection[] {
    const peerIds = this.roomConnections.get(roomId);
    if (!peerIds) return [];
    
    return Array.from(peerIds)
      .map(id => this.peers.get(id))
      .filter(peer => peer !== undefined) as PeerConnection[];
  }

  /**
   * Get peer by ID
   */
  getPeer(peerId: string): PeerConnection | undefined {
    return this.peers.get(peerId);
  }

  /**
   * Get all active rooms
   */
  getActiveRooms(): { roomId: string; peerCount: number; peers: PeerConnection[] }[] {
    return Array.from(this.roomConnections.entries()).map(([roomId, peerIds]) => ({
      roomId,
      peerCount: peerIds.size,
      peers: this.getRoomPeers(roomId),
    }));
  }

  /**
   * Clean up inactive peers
   */
  cleanupInactivePeers(timeoutMinutes: number = 30): number {
    const cutoffTime = new Date(Date.now() - timeoutMinutes * 60 * 1000);
    let cleanedCount = 0;

    for (const [peerId, peer] of this.peers.entries()) {
      if (peer.lastActivity < cutoffTime) {
        this.logger.log(`Cleaning up inactive peer: ${peerId}`);
        this.unregisterPeer(peerId);
        cleanedCount++;
      }
    }

    return cleanedCount;
  }

  private handleOffer(sender: PeerConnection, offer: RTCSessionDescriptionInit): void {
    this.logger.log(`Processing offer from ${sender.id}`);
    // Additional offer processing logic can be added here
    // For example, validation, logging, or modification of the offer
  }

  private handleAnswer(sender: PeerConnection, answer: RTCSessionDescriptionInit): void {
    this.logger.log(`Processing answer from ${sender.id}`);
    // Additional answer processing logic can be added here
  }

  private handleIceCandidate(sender: PeerConnection, candidate: RTCIceCandidateInit): void {
    this.logger.log(`Processing ICE candidate from ${sender.id}`);
    // Additional ICE candidate processing logic can be added here
  }

  private handleConnectionState(sender: PeerConnection, state: any): void {
    this.logger.log(`Connection state update from ${sender.id}: ${JSON.stringify(state)}`);
    
    if (state.connectionState) {
      sender.isConnected = state.connectionState === 'connected';
    }
  }

  /**
   * Get statistics about the signaling service
   */
  getStats() {
    const totalPeers = this.peers.size;
    const totalRooms = this.roomConnections.size;
    const connectedPeers = Array.from(this.peers.values()).filter(p => p.isConnected).length;
    
    const roomStats = Array.from(this.roomConnections.entries()).map(([roomId, peerIds]) => {
      const peers = Array.from(peerIds).map(id => this.peers.get(id)).filter(Boolean);
      const robots = peers.filter(p => p!.clientType === 'robot').length;
      const controllers = peers.filter(p => p!.clientType === 'controller').length;
      
      return {
        roomId,
        totalPeers: peerIds.size,
        robots,
        controllers,
        connectedPeers: peers.filter(p => p!.isConnected).length,
      };
    });

    return {
      totalPeers,
      totalRooms,
      connectedPeers,
      rooms: roomStats,
    };
  }
}
