import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';

interface SignalingMessage {
  type: 'offer' | 'answer' | 'ice-candidate';
  data: any;
  targetId?: string;
}

interface DataChannelMessage {
  type: 'data';
  data: any;
  targetId?: string;
}

@WebSocketGateway({
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
  },
  namespace: '/signaling',
})
export class SignalingGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(SignalingGateway.name);
  private clients = new Map<string, { socketId: string; isReady: boolean }>();

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
    this.clients.set(client.id, { socketId: client.id, isReady: false });

    // 通知其他客户端有新用户加入
    client.broadcast.emit('user-connected', { clientId: client.id });
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
    this.clients.delete(client.id);

    // 通知其他客户端用户离开
    client.broadcast.emit('user-disconnected', { clientId: client.id });
  }

  @SubscribeMessage('ready')
  handleReady(@ConnectedSocket() client: Socket) {
    const clientInfo = this.clients.get(client.id);
    if (clientInfo) {
      clientInfo.isReady = true;
      this.logger.log(`Client ${client.id} is ready`);

      // 通知其他已准备的客户端
      client.broadcast.emit('user-ready', { clientId: client.id });

      // 发送当前已准备的客户端列表
      const readyClients = Array.from(this.clients.entries())
        .filter(([_, info]) => info.isReady && info.socketId !== client.id)
        .map(([id, _]) => id);

      client.emit('ready-clients', { clients: readyClients });
    }
  }

  @SubscribeMessage('offer')
  handleOffer(
    @ConnectedSocket() client: Socket,
    @MessageBody() message: SignalingMessage,
  ) {
    this.logger.log(`Offer from ${client.id} to ${message.targetId}`);

    if (message.targetId) {
      // 发送给指定客户端
      client.to(message.targetId).emit('offer', {
        ...message,
        senderId: client.id,
      });
    } else {
      // 广播给所有其他客户端
      client.broadcast.emit('offer', {
        ...message,
        senderId: client.id,
      });
    }
  }

  @SubscribeMessage('answer')
  handleAnswer(
    @ConnectedSocket() client: Socket,
    @MessageBody() message: SignalingMessage,
  ) {
    this.logger.log(`Answer from ${client.id} to ${message.targetId}`);

    if (message.targetId) {
      client.to(message.targetId).emit('answer', {
        ...message,
        senderId: client.id,
      });
    }
  }

  @SubscribeMessage('ice-candidate')
  handleIceCandidate(
    @ConnectedSocket() client: Socket,
    @MessageBody() message: SignalingMessage,
  ) {
    this.logger.log(`ICE candidate from ${client.id}`);

    if (message.targetId) {
      // 发送给指定客户端
      client.to(message.targetId).emit('ice-candidate', {
        ...message,
        senderId: client.id,
      });
    } else {
      // 广播给所有其他客户端
      client.broadcast.emit('ice-candidate', {
        ...message,
        senderId: client.id,
      });
    }
  }

  @SubscribeMessage('data-channel-message')
  handleDataChannelMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() message: DataChannelMessage,
  ) {
    this.logger.log(`Data channel message from ${client.id}`);

    if (message.targetId) {
      // 发送给指定客户端
      client.to(message.targetId).emit('data-channel-message', {
        ...message,
        senderId: client.id,
      });
    } else {
      // 广播给所有其他客户端
      client.broadcast.emit('data-channel-message', {
        ...message,
        senderId: client.id,
      });
    }
  }

  @SubscribeMessage('get-clients')
  handleGetClients(@ConnectedSocket() client: Socket) {
    const allClients = Array.from(this.clients.entries()).map(([id, info]) => ({
      clientId: id,
      isReady: info.isReady,
    }));

    client.emit('clients-list', { clients: allClients });
  }

  // 获取连接统计信息
  getStats() {
    const totalClients = this.clients.size;
    const readyClients = Array.from(this.clients.values()).filter(
      (client) => client.isReady,
    ).length;

    return {
      totalClients,
      readyClients,
      clients: Array.from(this.clients.entries()).map(([id, info]) => ({
        clientId: id,
        isReady: info.isReady,
      })),
    };
  }
}
