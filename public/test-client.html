<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Robot Dog WebRTC Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .video-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        video {
            width: 300px;
            height: 200px;
            background: #000;
            border-radius: 4px;
        }
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: black;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .movement-controls {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            max-width: 300px;
        }
        .movement-controls button {
            padding: 15px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <h1>🐕 Robot Dog WebRTC Test Client</h1>
    
    <div class="container">
        <h2>Connection Status</h2>
        <div id="status" class="status disconnected">Disconnected</div>
        
        <div class="controls">
            <div>
                <label>Room ID:</label>
                <input type="text" id="roomId" value="test-room" placeholder="Enter room ID">
            </div>
            <div>
                <label>Client Type:</label>
                <select id="clientType">
                    <option value="controller">Controller</option>
                    <option value="robot">Robot</option>
                </select>
            </div>
            <div>
                <label>Video Quality:</label>
                <select id="quality">
                    <option value="low">Low (640x480)</option>
                    <option value="medium" selected>Medium (1280x720)</option>
                    <option value="high">High (1920x1080)</option>
                    <option value="ultra">Ultra (4K)</option>
                </select>
            </div>
        </div>
        
        <div class="controls">
            <button id="connectBtn" class="btn-primary" onclick="connect()">Connect</button>
            <button id="disconnectBtn" class="btn-danger" onclick="disconnect()" disabled>Disconnect</button>
            <button id="startVideoBtn" class="btn-success" onclick="startVideo()" disabled>Start Video</button>
            <button id="stopVideoBtn" class="btn-warning" onclick="stopVideo()" disabled>Stop Video</button>
        </div>
    </div>

    <div class="container">
        <h2>Video Streams</h2>
        <div class="video-container">
            <div>
                <h3>Local Video</h3>
                <video id="localVideo" autoplay muted></video>
            </div>
            <div>
                <h3>Remote Video</h3>
                <video id="remoteVideo" autoplay></video>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>Robot Controls</h2>
        <div class="movement-controls">
            <div></div>
            <button class="btn-primary" onclick="sendMovement('forward')">↑ Forward</button>
            <div></div>
            <button class="btn-primary" onclick="sendMovement('left')">← Left</button>
            <button class="btn-danger" onclick="emergencyStop()">⏹ STOP</button>
            <button class="btn-primary" onclick="sendMovement('right')">→ Right</button>
            <div></div>
            <button class="btn-primary" onclick="sendMovement('backward')">↓ Backward</button>
            <div></div>
        </div>
        
        <div class="controls" style="margin-top: 20px;">
            <div>
                <label>Speed:</label>
                <input type="range" id="speed" min="0" max="100" value="50">
                <span id="speedValue">50</span>%
            </div>
            <div>
                <label>Camera Pan:</label>
                <input type="range" id="cameraPan" min="-180" max="180" value="0">
                <span id="panValue">0</span>°
            </div>
            <div>
                <label>Camera Tilt:</label>
                <input type="range" id="cameraTilt" min="-90" max="90" value="0">
                <span id="tiltValue">0</span>°
            </div>
        </div>
        
        <div class="controls">
            <button class="btn-success" onclick="sendCameraCommand()">Update Camera</button>
            <button class="btn-warning" onclick="getServerStats()">Get Server Stats</button>
        </div>
    </div>

    <div class="container">
        <h2>Event Log</h2>
        <div id="log" class="log"></div>
        <button class="btn-warning" onclick="clearLog()">Clear Log</button>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        let socket = null;
        let peerConnection = null;
        let localStream = null;
        let isConnected = false;

        // Update range input displays
        document.getElementById('speed').oninput = function() {
            document.getElementById('speedValue').textContent = this.value;
        };
        document.getElementById('cameraPan').oninput = function() {
            document.getElementById('panValue').textContent = this.value;
        };
        document.getElementById('cameraTilt').oninput = function() {
            document.getElementById('tiltValue').textContent = this.value;
        };

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateStatus(connected) {
            isConnected = connected;
            const statusDiv = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const startVideoBtn = document.getElementById('startVideoBtn');
            const stopVideoBtn = document.getElementById('stopVideoBtn');

            if (connected) {
                statusDiv.textContent = 'Connected';
                statusDiv.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                startVideoBtn.disabled = false;
            } else {
                statusDiv.textContent = 'Disconnected';
                statusDiv.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                startVideoBtn.disabled = true;
                stopVideoBtn.disabled = true;
            }
        }

        async function connect() {
            const roomId = document.getElementById('roomId').value;
            const clientType = document.getElementById('clientType').value;

            if (!roomId) {
                alert('Please enter a room ID');
                return;
            }

            try {
                socket = io('ws://localhost:3000/webrtc');
                
                socket.on('connect', () => {
                    log('Connected to server');
                    updateStatus(true);
                    
                    // Join room
                    socket.emit('join-room', { roomId, clientType });
                });

                socket.on('disconnect', () => {
                    log('Disconnected from server');
                    updateStatus(false);
                });

                socket.on('user-joined', (data) => {
                    log(`User joined: ${data.clientId} (${data.clientType})`);
                });

                socket.on('user-left', (data) => {
                    log(`User left: ${data.clientId}`);
                });

                socket.on('webrtc-signal', handleWebRTCSignal);
                socket.on('robot-control', handleRobotControl);
                socket.on('robot-status', handleRobotStatus);
                socket.on('error', (error) => {
                    log(`Error: ${error.message}`);
                });

            } catch (error) {
                log(`Connection error: ${error.message}`);
            }
        }

        function disconnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
            }
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
            }
            updateStatus(false);
            log('Disconnected');
        }

        async function startVideo() {
            try {
                const quality = document.getElementById('quality').value;
                
                // Get WebRTC config from server
                const response = await fetch(`http://localhost:3000/api/webrtc/config?quality=${quality}`);
                const config = await response.json();
                
                log(`Starting video with quality: ${quality}`);
                
                // Get user media
                localStream = await navigator.mediaDevices.getUserMedia(config.mediaConstraints);
                document.getElementById('localVideo').srcObject = localStream;
                
                // Create peer connection
                peerConnection = new RTCPeerConnection(config.rtcConfiguration);
                
                // Add local stream to peer connection
                localStream.getTracks().forEach(track => {
                    peerConnection.addTrack(track, localStream);
                });
                
                // Handle remote stream
                peerConnection.ontrack = (event) => {
                    document.getElementById('remoteVideo').srcObject = event.streams[0];
                    log('Received remote stream');
                };
                
                // Handle ICE candidates
                peerConnection.onicecandidate = (event) => {
                    if (event.candidate && socket) {
                        socket.emit('webrtc-signal', {
                            type: 'ice-candidate',
                            data: event.candidate
                        });
                    }
                };
                
                document.getElementById('stopVideoBtn').disabled = false;
                log('Video started successfully');
                
            } catch (error) {
                log(`Error starting video: ${error.message}`);
            }
        }

        function stopVideo() {
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
                document.getElementById('localVideo').srcObject = null;
            }
            if (peerConnection) {
                peerConnection.close();
                peerConnection = null;
                document.getElementById('remoteVideo').srcObject = null;
            }
            document.getElementById('stopVideoBtn').disabled = true;
            log('Video stopped');
        }

        function handleWebRTCSignal(message) {
            log(`Received WebRTC signal: ${message.type}`);
            // Handle WebRTC signaling messages
            // This would include offer/answer/ice-candidate handling
        }

        function handleRobotControl(message) {
            log(`Robot control: ${message.type} - ${JSON.stringify(message.data)}`);
        }

        function handleRobotStatus(message) {
            log(`Robot status: ${JSON.stringify(message)}`);
        }

        function sendMovement(direction) {
            if (!socket || !isConnected) {
                alert('Not connected to server');
                return;
            }

            const speed = parseInt(document.getElementById('speed').value);
            const command = {
                type: 'move',
                data: {
                    direction: direction,
                    speed: speed
                }
            };

            socket.emit('robot-control', command);
            log(`Sent movement command: ${direction} at ${speed}% speed`);
        }

        function emergencyStop() {
            if (!socket || !isConnected) {
                alert('Not connected to server');
                return;
            }

            const command = {
                type: 'stop',
                data: {}
            };

            socket.emit('robot-control', command);
            log('EMERGENCY STOP sent');
        }

        function sendCameraCommand() {
            if (!socket || !isConnected) {
                alert('Not connected to server');
                return;
            }

            const pan = parseInt(document.getElementById('cameraPan').value);
            const tilt = parseInt(document.getElementById('cameraTilt').value);
            
            const command = {
                type: 'camera-control',
                data: {
                    cameraAngle: { x: pan, y: tilt }
                }
            };

            socket.emit('robot-control', command);
            log(`Sent camera command: pan=${pan}°, tilt=${tilt}°`);
        }

        async function getServerStats() {
            try {
                const response = await fetch('http://localhost:3000/api/webrtc/health');
                const stats = await response.json();
                log(`Server stats: ${JSON.stringify(stats, null, 2)}`);
            } catch (error) {
                log(`Error getting server stats: ${error.message}`);
            }
        }

        // Initialize
        log('Robot Dog WebRTC Test Client loaded');
    </script>
</body>
</html>
