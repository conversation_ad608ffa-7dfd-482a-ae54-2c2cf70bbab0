/**
 * WebRTC SDK for Browser and Mobile
 * 支持流媒体传输和数据通道通信
 */
class WebRTCSDK {
  constructor(serverUrl = 'http://localhost:3000') {
    this.serverUrl = serverUrl;
    this.socket = null;
    this.peerConnection = null;
    this.localStream = null;
    this.remoteStream = null;
    this.dataChannel = null;
    this.isConnected = false;
    this.clientId = null;
    this.config = null;

    // 事件监听器
    this.eventListeners = {};

    // 文件传输
    this.fileTransfers = new Map();
  }

  /**
   * 初始化 SDK
   */
  async init(quality = 'medium') {
    try {
      // 获取 WebRTC 配置
      const response = await fetch(
        `${this.serverUrl}/api/webrtc/config?quality=${quality}`,
      );
      this.config = await response.json();

      // 连接信令服务器
      await this.connectSignaling();

      this.emit('initialized', { config: this.config });
      return true;
    } catch (error) {
      this.emit('error', { type: 'initialization', error: error.message });
      throw error;
    }
  }

  /**
   * 连接信令服务器
   */
  async connectSignaling() {
    return new Promise((resolve, reject) => {
      this.socket = io(`${this.serverUrl}/signaling`);

      this.socket.on('connect', () => {
        this.isConnected = true;
        this.clientId = this.socket.id;
        this.emit('connected', { clientId: this.clientId });
        resolve();
      });

      this.socket.on('disconnect', () => {
        this.isConnected = false;
        this.emit('disconnected');
      });

      this.socket.on('connect_error', (error) => {
        reject(error);
      });

      // 信令事件
      this.socket.on('user-connected', (data) => {
        this.emit('user-connected', data);
      });

      this.socket.on('user-disconnected', (data) => {
        this.emit('user-disconnected', data);
      });

      this.socket.on('user-ready', (data) => {
        this.emit('user-ready', data);
      });

      this.socket.on('offer', (data) => {
        this.handleOffer(data);
      });

      this.socket.on('answer', (data) => {
        this.handleAnswer(data);
      });

      this.socket.on('ice-candidate', (data) => {
        this.handleIceCandidate(data);
      });

      this.socket.on('data-channel-message', (data) => {
        this.emit('data-channel-message', data);
      });
    });
  }

  /**
   * 标记为准备就绪
   */
  ready() {
    if (this.socket) {
      this.socket.emit('ready');
    }
  }

  /**
   * 创建对等连接
   */
  async createPeerConnection() {
    this.peerConnection = new RTCPeerConnection(this.config.rtcConfiguration);

    // ICE 候选事件
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate && this.socket) {
        this.socket.emit('ice-candidate', {
          type: 'ice-candidate',
          data: event.candidate,
        });
      }
    };

    // 远程流事件
    this.peerConnection.ontrack = (event) => {
      this.remoteStream = event.streams[0];
      this.emit('remote-stream', { stream: this.remoteStream });
    };

    // 连接状态变化
    this.peerConnection.onconnectionstatechange = () => {
      this.emit('connection-state-change', {
        state: this.peerConnection.connectionState,
      });
    };

    // 数据通道事件
    this.peerConnection.ondatachannel = (event) => {
      const channel = event.channel;
      this.setupDataChannel(channel);
    };

    return this.peerConnection;
  }

  /**
   * 获取本地媒体流
   */
  async getLocalStream(constraints = null) {
    try {
      const mediaConstraints = constraints || this.config.mediaConstraints;
      this.localStream =
        await navigator.mediaDevices.getUserMedia(mediaConstraints);

      this.emit('local-stream', { stream: this.localStream });
      return this.localStream;
    } catch (error) {
      this.emit('error', { type: 'media', error: error.message });
      throw error;
    }
  }

  /**
   * 获取屏幕共享流
   */
  async getScreenShare() {
    try {
      const response = await fetch(
        `${this.serverUrl}/api/webrtc/config/screen-share`,
      );
      const config = await response.json();

      this.localStream = await navigator.mediaDevices.getDisplayMedia(
        config.mediaConstraints,
      );

      this.emit('local-stream', { stream: this.localStream, type: 'screen' });
      return this.localStream;
    } catch (error) {
      this.emit('error', { type: 'screen-share', error: error.message });
      throw error;
    }
  }

  /**
   * 创建 Offer
   */
  async createOffer(targetId = null) {
    if (!this.peerConnection) {
      await this.createPeerConnection();
    }

    // 添加本地流
    if (this.localStream) {
      this.localStream.getTracks().forEach((track) => {
        this.peerConnection.addTrack(track, this.localStream);
      });
    }

    // 创建数据通道
    this.createDataChannel();

    const offer = await this.peerConnection.createOffer();
    await this.peerConnection.setLocalDescription(offer);

    // 发送 offer
    this.socket.emit('offer', {
      type: 'offer',
      data: offer,
      targetId: targetId,
    });

    return offer;
  }

  /**
   * 处理 Offer
   */
  async handleOffer(data) {
    if (!this.peerConnection) {
      await this.createPeerConnection();
    }

    await this.peerConnection.setRemoteDescription(data.data);

    // 添加本地流
    if (this.localStream) {
      this.localStream.getTracks().forEach((track) => {
        this.peerConnection.addTrack(track, this.localStream);
      });
    }

    const answer = await this.peerConnection.createAnswer();
    await this.peerConnection.setLocalDescription(answer);

    // 发送 answer
    this.socket.emit('answer', {
      type: 'answer',
      data: answer,
      targetId: data.senderId,
    });

    this.emit('offer-received', data);
  }

  /**
   * 处理 Answer
   */
  async handleAnswer(data) {
    await this.peerConnection.setRemoteDescription(data.data);
    this.emit('answer-received', data);
  }

  /**
   * 处理 ICE 候选
   */
  async handleIceCandidate(data) {
    if (this.peerConnection) {
      await this.peerConnection.addIceCandidate(data.data);
    }
  }

  /**
   * 创建数据通道
   */
  createDataChannel(label = 'dataChannel') {
    if (!this.peerConnection) {
      throw new Error('Peer connection not created');
    }

    this.dataChannel = this.peerConnection.createDataChannel(
      label,
      this.config.dataChannelConfig,
    );
    this.setupDataChannel(this.dataChannel);

    return this.dataChannel;
  }

  /**
   * 设置数据通道事件
   */
  setupDataChannel(channel) {
    channel.onopen = () => {
      this.emit('data-channel-open', { channel });
    };

    channel.onclose = () => {
      this.emit('data-channel-close', { channel });
    };

    channel.onmessage = (event) => {
      this.handleDataChannelMessage(event.data);
    };

    channel.onerror = (error) => {
      this.emit('data-channel-error', { error });
    };
  }

  /**
   * 发送数据通道消息
   */
  sendDataChannelMessage(data, type = 'text') {
    if (!this.dataChannel || this.dataChannel.readyState !== 'open') {
      throw new Error('Data channel not open');
    }

    const message = {
      type,
      data,
      timestamp: Date.now(),
    };

    this.dataChannel.send(JSON.stringify(message));
  }

  /**
   * 处理数据通道消息
   */
  handleDataChannelMessage(data) {
    try {
      const message = JSON.parse(data);

      if (message.type === 'file-chunk') {
        this.handleFileChunk(message);
      } else {
        this.emit('data-channel-message', message);
      }
    } catch (error) {
      // 处理二进制数据
      this.emit('data-channel-binary', { data });
    }
  }

  /**
   * 发送文件
   */
  async sendFile(file, targetId = null, chunkSize = 16384) {
    const transferId = `transfer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const totalChunks = Math.ceil(file.size / chunkSize);

    // 开始文件传输
    const response = await fetch(
      `${this.serverUrl}/api/webrtc/file-transfers/start`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          senderId: this.clientId,
          receiverId: targetId,
          fileName: file.name,
          fileSize: file.size,
          mimeType: file.type,
          totalChunks,
        }),
      },
    );

    const result = await response.json();

    // 发送文件信息
    this.sendDataChannelMessage(
      {
        transferId: result.transferId,
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        totalChunks,
      },
      'file-start',
    );

    // 分块发送文件
    for (let i = 0; i < totalChunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = file.slice(start, end);
      const arrayBuffer = await chunk.arrayBuffer();

      this.sendDataChannelMessage(
        {
          transferId: result.transferId,
          chunkIndex: i,
          data: Array.from(new Uint8Array(arrayBuffer)),
        },
        'file-chunk',
      );

      // 发送进度事件
      const progress = ((i + 1) / totalChunks) * 100;
      this.emit('file-send-progress', {
        transferId: result.transferId,
        progress,
        fileName: file.name,
      });
    }

    this.emit('file-send-complete', {
      transferId: result.transferId,
      fileName: file.name,
    });

    return result.transferId;
  }

  /**
   * 处理文件块
   */
  handleFileChunk(message) {
    const { transferId, chunkIndex, data } = message.data;

    if (!this.fileTransfers.has(transferId)) {
      this.fileTransfers.set(transferId, {
        chunks: new Map(),
        receivedChunks: 0,
      });
    }

    const transfer = this.fileTransfers.get(transferId);
    transfer.chunks.set(chunkIndex, new Uint8Array(data));
    transfer.receivedChunks++;

    this.emit('file-receive-progress', {
      transferId,
      receivedChunks: transfer.receivedChunks,
    });
  }

  /**
   * 事件监听
   */
  on(event, callback) {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = [];
    }
    this.eventListeners[event].push(callback);
  }

  /**
   * 移除事件监听
   */
  off(event, callback) {
    if (this.eventListeners[event]) {
      const index = this.eventListeners[event].indexOf(callback);
      if (index > -1) {
        this.eventListeners[event].splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].forEach((callback) => {
        callback(data);
      });
    }
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.localStream) {
      this.localStream.getTracks().forEach((track) => track.stop());
    }

    if (this.peerConnection) {
      this.peerConnection.close();
    }

    if (this.socket) {
      this.socket.disconnect();
    }

    this.isConnected = false;
    this.emit('disconnected');
  }

  /**
   * 获取连接统计
   */
  async getStats() {
    if (!this.peerConnection) {
      return null;
    }

    const stats = await this.peerConnection.getStats();
    const result = {};

    stats.forEach((report) => {
      result[report.id] = report;
    });

    return result;
  }
}

// 导出 SDK
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WebRTCSDK;
} else if (typeof window !== 'undefined') {
  window.WebRTCSDK = WebRTCSDK;
}
