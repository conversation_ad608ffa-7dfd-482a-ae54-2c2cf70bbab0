<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC Framework Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .video-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        video {
            width: 300px;
            height: 200px;
            background: #000;
            border-radius: 4px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected { background: #d4edda; color: #155724; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .data-channel {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .file-transfer {
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <h1>🌐 WebRTC Framework Test</h1>
    
    <div class="container">
        <h2>Connection Status</h2>
        <div id="status" class="status disconnected">Disconnected</div>
        
        <div class="controls">
            <select id="quality">
                <option value="low">Low Quality</option>
                <option value="medium" selected>Medium Quality</option>
                <option value="high">High Quality</option>
            </select>
            <button id="initBtn" class="btn-primary" onclick="initSDK()">Initialize</button>
            <button id="readyBtn" class="btn-success" onclick="markReady()" disabled>Ready</button>
            <button id="disconnectBtn" class="btn-danger" onclick="disconnect()" disabled>Disconnect</button>
        </div>
    </div>

    <div class="container">
        <h2>Media Streams</h2>
        <div class="video-container">
            <div>
                <h3>Local Video</h3>
                <video id="localVideo" autoplay muted></video>
            </div>
            <div>
                <h3>Remote Video</h3>
                <video id="remoteVideo" autoplay></video>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn-success" onclick="startVideo()">Start Video</button>
            <button class="btn-success" onclick="startAudio()">Audio Only</button>
            <button class="btn-warning" onclick="startScreenShare()">Screen Share</button>
            <button class="btn-danger" onclick="stopMedia()">Stop Media</button>
        </div>
    </div>

    <div class="container">
        <h2>WebRTC Connection</h2>
        <div class="controls">
            <button class="btn-primary" onclick="createOffer()">Create Offer</button>
            <button class="btn-warning" onclick="getStats()">Get Stats</button>
        </div>
        <div id="connectionState">Not connected</div>
    </div>

    <div class="container">
        <h2>Data Channel</h2>
        <div class="data-channel">
            <input type="text" id="messageInput" placeholder="Enter message" style="flex: 1;">
            <button class="btn-primary" onclick="sendMessage()">Send</button>
        </div>
        <div id="dataChannelMessages" style="margin-top: 10px; max-height: 150px; overflow-y: auto;"></div>
    </div>

    <div class="container">
        <h2>File Transfer</h2>
        <div class="controls">
            <input type="file" id="fileInput">
            <button class="btn-success" onclick="sendFile()">Send File</button>
        </div>
        <div id="fileTransfers"></div>
    </div>

    <div class="container">
        <h2>Event Log</h2>
        <div id="log" class="log"></div>
        <button class="btn-warning" onclick="clearLog()">Clear Log</button>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="webrtc-sdk.js"></script>
    <script>
        let sdk = null;
        let isInitialized = false;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateStatus(connected) {
            const statusDiv = document.getElementById('status');
            const initBtn = document.getElementById('initBtn');
            const readyBtn = document.getElementById('readyBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');

            if (connected) {
                statusDiv.textContent = 'Connected';
                statusDiv.className = 'status connected';
                initBtn.disabled = true;
                readyBtn.disabled = false;
                disconnectBtn.disabled = false;
            } else {
                statusDiv.textContent = 'Disconnected';
                statusDiv.className = 'status disconnected';
                initBtn.disabled = false;
                readyBtn.disabled = true;
                disconnectBtn.disabled = true;
            }
        }

        async function initSDK() {
            try {
                const quality = document.getElementById('quality').value;
                sdk = new WebRTCSDK();
                
                // 设置事件监听器
                sdk.on('initialized', (data) => {
                    log('SDK initialized');
                    isInitialized = true;
                });

                sdk.on('connected', (data) => {
                    log(`Connected to signaling server: ${data.clientId}`);
                    updateStatus(true);
                });

                sdk.on('disconnected', () => {
                    log('Disconnected from signaling server');
                    updateStatus(false);
                });

                sdk.on('user-connected', (data) => {
                    log(`User connected: ${data.clientId}`);
                });

                sdk.on('user-ready', (data) => {
                    log(`User ready: ${data.clientId}`);
                });

                sdk.on('local-stream', (data) => {
                    document.getElementById('localVideo').srcObject = data.stream;
                    log(`Local stream started: ${data.type || 'camera'}`);
                });

                sdk.on('remote-stream', (data) => {
                    document.getElementById('remoteVideo').srcObject = data.stream;
                    log('Remote stream received');
                });

                sdk.on('connection-state-change', (data) => {
                    document.getElementById('connectionState').textContent = `Connection: ${data.state}`;
                    log(`Connection state: ${data.state}`);
                });

                sdk.on('data-channel-open', () => {
                    log('Data channel opened');
                });

                sdk.on('data-channel-message', (data) => {
                    displayDataChannelMessage(data);
                });

                sdk.on('file-send-progress', (data) => {
                    updateFileProgress(data.transferId, data.progress, 'Sending');
                });

                sdk.on('error', (data) => {
                    log(`Error (${data.type}): ${data.error}`);
                });

                await sdk.init(quality);
                
            } catch (error) {
                log(`Initialization failed: ${error.message}`);
            }
        }

        function markReady() {
            if (sdk) {
                sdk.ready();
                log('Marked as ready');
            }
        }

        function disconnect() {
            if (sdk) {
                sdk.disconnect();
                sdk = null;
                isInitialized = false;
            }
        }

        async function startVideo() {
            if (!sdk) return;
            try {
                await sdk.getLocalStream();
            } catch (error) {
                log(`Failed to start video: ${error.message}`);
            }
        }

        async function startAudio() {
            if (!sdk) return;
            try {
                await sdk.getLocalStream({ video: false, audio: true });
            } catch (error) {
                log(`Failed to start audio: ${error.message}`);
            }
        }

        async function startScreenShare() {
            if (!sdk) return;
            try {
                await sdk.getScreenShare();
            } catch (error) {
                log(`Failed to start screen share: ${error.message}`);
            }
        }

        function stopMedia() {
            if (sdk && sdk.localStream) {
                sdk.localStream.getTracks().forEach(track => track.stop());
                document.getElementById('localVideo').srcObject = null;
                log('Local media stopped');
            }
        }

        async function createOffer() {
            if (!sdk) return;
            try {
                await sdk.createOffer();
                log('Offer created and sent');
            } catch (error) {
                log(`Failed to create offer: ${error.message}`);
            }
        }

        async function getStats() {
            if (!sdk) return;
            try {
                const stats = await sdk.getStats();
                log(`Connection stats: ${Object.keys(stats).length} reports`);
                console.log('WebRTC Stats:', stats);
            } catch (error) {
                log(`Failed to get stats: ${error.message}`);
            }
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || !sdk || !sdk.dataChannel) return;
            
            try {
                sdk.sendDataChannelMessage(message, 'text');
                displayDataChannelMessage({ type: 'text', data: message, timestamp: Date.now() }, true);
                input.value = '';
            } catch (error) {
                log(`Failed to send message: ${error.message}`);
            }
        }

        function displayDataChannelMessage(message, isSent = false) {
            const container = document.getElementById('dataChannelMessages');
            const div = document.createElement('div');
            div.style.cssText = `
                padding: 5px;
                margin: 2px 0;
                border-radius: 4px;
                background: ${isSent ? '#e3f2fd' : '#f5f5f5'};
                border-left: 3px solid ${isSent ? '#2196f3' : '#4caf50'};
            `;
            div.textContent = `${isSent ? 'Sent' : 'Received'}: ${message.data}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        async function sendFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file || !sdk) return;
            
            try {
                const transferId = await sdk.sendFile(file);
                createFileTransferUI(transferId, file.name, 0, 'Sending');
                log(`Started sending file: ${file.name}`);
            } catch (error) {
                log(`Failed to send file: ${error.message}`);
            }
        }

        function createFileTransferUI(transferId, fileName, progress, status) {
            const container = document.getElementById('fileTransfers');
            const div = document.createElement('div');
            div.className = 'file-transfer';
            div.id = `transfer-${transferId}`;
            div.innerHTML = `
                <div><strong>${fileName}</strong> - ${status}</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${progress}%"></div>
                </div>
                <div>${progress.toFixed(1)}%</div>
            `;
            container.appendChild(div);
        }

        function updateFileProgress(transferId, progress, status) {
            const element = document.getElementById(`transfer-${transferId}`);
            if (element) {
                const progressFill = element.querySelector('.progress-fill');
                const progressText = element.querySelector('div:last-child');
                progressFill.style.width = `${progress}%`;
                progressText.textContent = `${progress.toFixed(1)}%`;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('WebRTC Framework Test loaded');
            
            // Enter 键发送消息
            document.getElementById('messageInput').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        });
    </script>
</body>
</html>
